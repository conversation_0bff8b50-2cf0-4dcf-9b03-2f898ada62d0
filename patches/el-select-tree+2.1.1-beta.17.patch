diff --git a/node_modules/el-select-tree/lib/es/components/utils.js b/node_modules/el-select-tree/lib/es/components/utils.js
index b8f5a89..b69ccd4 100644
--- a/node_modules/el-select-tree/lib/es/components/utils.js
+++ b/node_modules/el-select-tree/lib/es/components/utils.js
@@ -316,7 +316,7 @@ function getCompoundVal(data, prop) {
   if (prop instanceof Function) {
     return prop.apply(void 0, [data].concat(args));
   } else {
-    return data[prop];
+    return data ? data[prop]: data;
   }
 }
 var empty = Vue.observable({});
