/* ==================
          flex布局
 ==================== */

.sa-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.sa-flex-1 {
  flex: 1;
}

.sa-flex-0 {
  flex-shrink: 0;
}

.sa-flex-col {
  display: flex;
  flex-direction: column;
}

.sa-flex-wrap {
  flex-wrap: wrap;
}

.sa-flex-nowrap {
  flex-wrap: nowrap;
}

.sa-col-center {
  align-items: center;
}

.sa-col-top {
  align-items: flex-start;
}

.sa-col-bottom {
  align-items: flex-end;
}

.sa-row-center {
  justify-content: center;
}

.sa-row-left {
  justify-content: flex-start;
}

.sa-row-right {
  justify-content: flex-end;
}

.sa-row-between {
  justify-content: space-between;
}

.sa-row-around {
  justify-content: space-around;
}

.sa-self-start {
  align-self: flex-start;
}

.sa-self-end {
  align-self: flex-end;
}

.sa-self-center {
  align-self: center;
}

/* ==================
    width: 宽度
 ==================== */
@for $i from 0 through 1000 {
  // 得出：sa-width-30或者sa-w-30
  .sa-width-#{$i},
  .sa-w-#{$i} {
    width: $i + px !important;
  }
}

/* ==================
    margin padding: 内外边距
 ==================== */
@for $i from 0 through 100 {
  // 只要双数和能被5除尽的数
  @if $i % 2==0 or $i % 5==0 {
    // 得出：u-margin-30或者u-m-30
    .sa-margin-#{$i},
    .sa-m-#{$i} {
      margin: $i + px !important;
    }

    // 得出：u-padding-30或者u-p-30
    .sa-padding-#{$i},
    .sa-p-#{$i} {
      padding: $i + px !important;
    }

    @each $short, $long in l left, t top, r right, b bottom {
      // 缩写版，结果如： u-m-l-30
      // 定义外边距
      .sa-m-#{$short}-#{$i} {
        margin-#{$long}: $i + px !important;
      }

      // 定义内边距
      .sa-p-#{$short}-#{$i} {
        padding-#{$long}: $i + px !important;
      }

      // 完整版，结果如：u-margin-left-30
      // 定义外边距
      .sa-margin-#{$long}-#{$i} {
        margin-#{$long}: $i + px !important;
      }

      // 定义内边距
      .sa-padding-#{$long}-#{$i} {
        padding-#{$long}: $i + px !important;
      }
    }
  }
}

/* ==================
    溢出省略号
    @param {Number} 行数
 ==================== */
.sa-table-line-1 {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  height: 23px;
  line-height: 23px;
}

.sa-table-line-2 {
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  // height: 2;
  line-height: 1;
}

@mixin ellipsis($rowCount: 1) {
  @if $rowCount <=1 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  } @else {
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: $rowCount;
    -webkit-box-orient: vertical;
  }
}

@for $i from 1 through 6 {
  .sa-line-#{$i} {
    @include ellipsis($i);
  }
}

.add-edit-dialog {
  .el-form-item:not(.ignore-form-item *) {
    width: calc(50% - 18px);
    margin-right: 0;
    &.sa-w-full {
      width: 100%;
      display: flex;
      .el-form-item__content {
        flex: 1;
      }
    }
  }
}

.sa-color--success {
  color: #67c23a;
}
.sa-color--danger {
  color: #f56c6c;
}
.sa-color--warning {
  color: #e6a23c;
}

.is-scrolling-none + .el-table__fixed-right {
    height: 100% !important;
}
