import request from '@/utils/request'

// 查询解决方案库列表
export function listSolution(query) {
  return request({
    url: '/res/solution/library/list',
    method: 'get',
    params: query
  })
}

// 查询解决方案库详细
export function getSolution(id) {
  return request({
    url: '/res/solution/library/' + id,
    method: 'get'
  })
}

// 新增解决方案库
export function addSolution(data) {
  return request({
    url: '/res/solution/library',
    method: 'post',
    data: data
  })
}

// 修改解决方案库
export function updateSolution(data) {
  return request({
    url: '/res/solution/library',
    method: 'put',
    data: data
  })
}

// 删除解决方案库
export function delSolution(id) {
  return request({
    url: '/res/solution/library/' + id,
    method: 'delete'
  })
}

// 导出解决方案库
export function exportSolution(query) {
  return request({
    url: '/res/solution/library/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/solution/library/importTemplate',
    method: 'get'
  })
}
