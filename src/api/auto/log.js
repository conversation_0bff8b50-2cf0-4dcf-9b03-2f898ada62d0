import request from '@/utils/request'

// 查询4a采集日志记录列表
export function listLog(query) {
  return request({
    url: '/res/log/list',
    method: 'get',
    params: query
  })
}

// 查询4a采集日志记录详细
export function getLog(id) {
  return request({
    url: '/res/log/' + id,
    method: 'get'
  })
}

// 新增4a采集日志记录
export function addLog(data) {
  return request({
    url: '/res/log',
    method: 'post',
    data: data
  })
}

// 修改4a采集日志记录
export function updateLog(data) {
  return request({
    url: '/res/log',
    method: 'put',
    data: data
  })
}

// 删除4a采集日志记录
export function delLog(id) {
  return request({
    url: '/res/log/' + id,
    method: 'delete'
  })
}

// 导出4a采集日志记录
export function exportLog(query) {
  return request({
    url: '/res/log/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/log/importTemplate',
    method: 'get'
  })
}
