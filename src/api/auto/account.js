import request from '@/utils/request'

// 查询4a指令通道程序主账号管理列表
export function listNumber(query) {
  return request({
    url: '/res/number/list',
    method: 'get',
    params: query
  })
}

// 查询4a指令通道程序主账号管理详细
export function getNumber(id) {
  return request({
    url: '/res/number/' + id,
    method: 'get'
  })
}

// 新增4a指令通道程序主账号管理
export function addNumber(data) {
  return request({
    url: '/res/number',
    method: 'post',
    data: data
  })
}

// 修改4a指令通道程序主账号管理
export function updateNumber(data) {
  return request({
    url: '/res/number',
    method: 'put',
    data: data
  })
}

// 删除4a指令通道程序主账号管理
export function delNumber(id) {
  return request({
    url: '/res/number/' + id,
    method: 'delete'
  })
}

// 导出4a指令通道程序主账号管理
export function exportNumber(query) {
  return request({
    url: '/res/number/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/number/importTemplate',
    method: 'get'
  })
}
