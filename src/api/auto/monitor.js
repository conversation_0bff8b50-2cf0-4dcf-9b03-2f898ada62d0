import request from '@/utils/request'

// 查询4A前置服务健康检查列表
export function listMonitor(query) {
  return request({
    url: '/res/monitor/list',
    method: 'get',
    params: query
  })
}

// 查询4A前置服务健康检查详细
export function getMonitor(id) {
  return request({
    url: '/res/monitor/' + id,
    method: 'get'
  })
}

// 新增4A前置服务健康检查
export function addMonitor(data) {
  return request({
    url: '/res/monitor',
    method: 'post',
    data: data
  })
}

// 修改4A前置服务健康检查
export function updateMonitor(data) {
  return request({
    url: '/res/monitor',
    method: 'put',
    data: data
  })
}

// 删除4A前置服务健康检查
export function delMonitor(id) {
  return request({
    url: '/res/monitor/' + id,
    method: 'delete'
  })
}

// 导出4A前置服务健康检查
export function exportMonitor(query) {
  return request({
    url: '/res/monitor/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/monitor/importTemplate',
    method: 'get'
  })
}
