import request from '@/utils/request'

// 查询补丁集市列表
export function listPatch(query) {
  return request({
    url: '/res/patch/market/list',
    method: 'get',
    params: query
  })
}

// 查询补丁集市详细
export function getPatch(id) {
  return request({
    url: '/res/patch/market/' + id,
    method: 'get'
  })
}

// 新增补丁集市
export function addPatch(data) {
  return request({
    url: '/res/patch/market',
    method: 'post',
    data: data
  })
}

// 修改补丁集市
export function updatePatch(data) {
  return request({
    url: '/res/patch/market',
    method: 'put',
    data: data
  })
}

// 删除补丁集市
export function delPatch(id) {
  return request({
    url: '/res/patch/market/' + id,
    method: 'delete'
  })
}

// 导出补丁集市
export function exportPatch(query) {
  return request({
    url: '/res/patch/market/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/patch/market/importTemplate',
    method: 'get'
  })
}
