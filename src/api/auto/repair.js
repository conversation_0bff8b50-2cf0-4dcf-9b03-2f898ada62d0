import request from '@/utils/request'

// 查询安全问题修复脚本列表
export function listRepair(query) {
  return request({
    url: '/res/repair/script/list',
    method: 'get',
    params: query
  })
}

// 查询安全问题修复脚本详细
export function getRepair(id) {
  return request({
    url: '/res/repair/script/' + id,
    method: 'get'
  })
}

// 新增安全问题修复脚本
export function addRepair(data) {
  return request({
    url: '/res/repair/script',
    method: 'post',
    data: data
  })
}

// 修改安全问题修复脚本
export function updateRepair(data) {
  return request({
    url: '/res/repair/script',
    method: 'put',
    data: data
  })
}

// 删除安全问题修复脚本
export function delRepair(id) {
  return request({
    url: '/res/repair/script/' + id,
    method: 'delete'
  })
}

// 导出安全问题修复脚本
export function exportRepair(query) {
  return request({
    url: '/res/repair/script/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/repair/script/importTemplate',
    method: 'get'
  })
}
