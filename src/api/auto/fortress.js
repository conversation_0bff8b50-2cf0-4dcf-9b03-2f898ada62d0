import request from '@/utils/request'

// 查询4A前置机信息列表
export function listPools(query) {
  return request({
    url: '/res/pools/list',
    method: 'get',
    params: query
  })
}

// 查询4A前置机信息详细
export function getPools(id) {
  return request({
    url: '/res/pools/' + id,
    method: 'get'
  })
}

// 新增4A前置机信息
export function addPools(data) {
  return request({
    url: '/res/pools',
    method: 'post',
    data: data
  })
}

// 修改4A前置机信息
export function updatePools(data) {
  return request({
    url: '/res/pools',
    method: 'put',
    data: data
  })
}

// 删除4A前置机信息
export function delPools(id) {
  return request({
    url: '/res/pools/' + id,
    method: 'delete'
  })
}

// 导出4A前置机信息
export function exportPools(query) {
  return request({
    url: '/res/pools/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/pools/importTemplate',
    method: 'get'
  })
}
