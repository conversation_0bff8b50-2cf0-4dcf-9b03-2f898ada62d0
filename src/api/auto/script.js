import request from '@/utils/request'

// 查询脚本日志记录列表
export function listScriptLog(query) {
  return request({
    url: '/res/script/log/list',
    method: 'get',
    params: query
  })
}

// 查询脚本日志记录详细
export function getScriptLog(id) {
  return request({
    url: '/res/script/log/' + id,
    method: 'get'
  })
}

// 新增脚本日志记录
export function addScriptLog(data) {
  return request({
    url: '/res/script/log',
    method: 'post',
    data: data
  })
}

// 修改脚本日志记录
export function updateScriptLog(data) {
  return request({
    url: '/res/script/log',
    method: 'put',
    data: data
  })
}

// 删除脚本日志记录
export function delScriptLog(id) {
  return request({
    url: '/res/script/log/' + id,
    method: 'delete'
  })
}

// 导出脚本日志记录
export function exportScriptLog(query) {
  return request({
    url: '/res/script/log/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/script/log/importTemplate',
    method: 'get'
  })
}
