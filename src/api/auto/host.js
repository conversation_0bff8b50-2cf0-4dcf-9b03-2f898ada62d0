import request from '@/utils/request'

// 查询4a主账户与主机授权列表
export function listMaster(query) {
  return request({
    url: '/res/master/list',
    method: 'get',
    params: query
  })
}

// 查询4a主账户与主机授权详细
export function getMaster(id) {
  return request({
    url: '/res/master/' + id,
    method: 'get'
  })
}

// 新增4a主账户与主机授权
export function addMaster(data) {
  return request({
    url: '/res/master',
    method: 'post',
    data: data
  })
}

// 修改4a主账户与主机授权
export function updateMaster(data) {
  return request({
    url: '/res/master',
    method: 'put',
    data: data
  })
}

// 删除4a主账户与主机授权
export function delMaster(id) {
  return request({
    url: '/res/master/' + id,
    method: 'delete'
  })
}

// 导出4a主账户与主机授权
export function exportMaster(query) {
  return request({
    url: '/res/master/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/master/importTemplate',
    method: 'get'
  })
}
