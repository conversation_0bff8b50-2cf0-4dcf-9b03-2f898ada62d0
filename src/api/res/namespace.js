import request from '@/utils/request'

// 查询磐基分区列表
export function listNamespace(query) {
  return request({
    url: '/res/namespace/list',
    method: 'get',
    params: query
  })
}

// 查询磐基分区详细
export function getNamespace(id) {
  return request({
    url: '/res/namespace/' + id,
    method: 'get'
  })
}

// 新增磐基分区
export function addNamespace(data) {
  return request({
    url: '/res/namespace',
    method: 'post',
    data: data
  })
}

// 修改磐基分区
export function updateNamespace(data) {
  return request({
    url: '/res/namespace',
    method: 'put',
    data: data
  })
}

// 删除磐基分区
export function delNamespace(id) {
  return request({
    url: '/res/namespace/' + id,
    method: 'delete'
  })
}

// 导出磐基分区
export function exportNamespace(query) {
  return request({
    url: '/res/namespace/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/namespace/importTemplate',
    method: 'get'
  })
}
