import request from '@/utils/request'

// 查询磐基租户资源列表
export function listTenant(query) {
  return request({
    url: '/res/tenant/list',
    method: 'get',
    params: query
  })
}

// 查询磐基租户资源详细
export function getTenant(id) {
  return request({
    url: '/res/tenant/' + id,
    method: 'get'
  })
}

// 新增磐基租户资源
export function addTenant(data) {
  return request({
    url: '/res/tenant',
    method: 'post',
    data: data
  })
}

// 修改磐基租户资源
export function updateTenant(data) {
  return request({
    url: '/res/tenant',
    method: 'put',
    data: data
  })
}

// 删除磐基租户资源
export function delTenant(id) {
  return request({
    url: '/res/tenant/' + id,
    method: 'delete'
  })
}

// 导出磐基租户资源
export function exportTenant(query) {
  return request({
    url: '/res/tenant/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/tenant/importTemplate',
    method: 'get'
  })
}
