import request from '@/utils/request'
import { download } from '@/utils/request'

// 查询中间件资产管理列表
export function listMiddlewareAsset(query) {
  return request({
    url: '/res/middlewareAssetManage/list',
    method: 'get',
    params: query
  })
}

// 查询中间件资产管理详细
export function getMiddlewareAsset(id) {
  return request({
    url: '/res/middlewareAssetManage/' + id,
    method: 'get'
  })
}

// 新增中间件资产管理
export function addMiddlewareAsset(data) {
  return request({
    url: '/res/middlewareAssetManage',
    method: 'post',
    data: data
  })
}

// 修改中间件资产管理
export function updateMiddlewareAsset(data) {
  return request({
    url: '/res/middlewareAssetManage',
    method: 'put',
    data: data
  })
}

// 删除中间件资产管理
export function delMiddlewareAsset(id) {
  return request({
    url: '/res/middlewareAssetManage/' + id,
    method: 'delete'
  })
}

// 导出中间件资产管理
export function exportMiddlewareAsset(query, name) {
  return download('/res/middlewareAssetManage/export', query, name)
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/middlewareAssetManage/importTemplate',
    method: 'get'
  })
}
