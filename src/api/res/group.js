import request from '@/utils/request'

// 查询磐基资源组列表
export function listGroup(query) {
  return request({
    url: '/res/group/list',
    method: 'get',
    params: query
  })
}

// 查询磐基资源组详细
export function getGroup(id) {
  return request({
    url: '/res/group/' + id,
    method: 'get'
  })
}

// 新增磐基资源组
export function addGroup(data) {
  return request({
    url: '/res/group',
    method: 'post',
    data: data
  })
}

// 修改磐基资源组
export function updateGroup(data) {
  return request({
    url: '/res/group',
    method: 'put',
    data: data
  })
}

// 删除磐基资源组
export function delGroup(id) {
  return request({
    url: '/res/group/' + id,
    method: 'delete'
  })
}

// 导出磐基资源组
export function exportGroup(query) {
  return request({
    url: '/res/group/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/group/importTemplate',
    method: 'get'
  })
}
