import request from '@/utils/request'

// 查询项目列表
export function listMirrorProject(query) {
  return request({
    url: '/res/project/list',
    method: 'get',
    params: query
  })
}

// 查询项目详细
export function getMirrorProject(id) {
  return request({
    url: '/res/project/' + id,
    method: 'get'
  })
}

// 新增项目
export function addMirrorProject(data) {
  return request({
    url: '/res/project',
    method: 'post',
    data: data
  })
}

// 修改项目
export function updateMirrorProject(data) {
  return request({
    url: '/res/project',
    method: 'put',
    data: data
  })
}

// 删除项目
export function delMirrorProject(id) {
  return request({
    url: '/res/project/' + id,
    method: 'delete'
  })
}

// 导出项目
export function exportMirrorProject(query) {
  return request({
    url: '/res/project/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/project/importTemplate',
    method: 'get'
  })
}
