import request from '@/utils/request'

// 查询镜像标签列表
export function listMirrorTag(query) {
  return request({
    url: '/res/pz/imageTag/list',
    method: 'get',
    params: query
  })
}

// 查询镜像标签详细
export function getMirrorTag(id) {
  return request({
    url: '/res/pz/imageTag/' + id,
    method: 'get'
  })
}

// 新增镜像标签
export function addMirrorTag(data) {
  return request({
    url: '/res/pz/imageTag',
    method: 'post',
    data: data
  })
}

// 修改镜像标签
export function updateMirrorTag(data) {
  return request({
    url: '/res/pz/imageTag',
    method: 'put',
    data: data
  })
}

// 删除镜像标签
export function delMirrorTag(id) {
  return request({
    url: '/res/pz/imageTag/' + id,
    method: 'delete'
  })
}

// 导出镜像标签
export function exportMirrorTag(query) {
  return request({
    url: '/res/pz/imageTag/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/pz/imageTag/importTemplate',
    method: 'get'
  })
}
