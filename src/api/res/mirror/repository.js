import request from '@/utils/request'

// 查询镜像仓库列表
export function listMirrorRepository(query) {
  return request({
    url: '/res/pz/imageRepo/list',
    method: 'get',
    params: query
  })
}

// 查询镜像仓库详细
export function getMirrorRepository(id) {
  return request({
    url: '/res/pz/imageRepo/' + id,
    method: 'get'
  })
}

// 新增镜像仓库
export function addMirrorRepository(data) {
  return request({
    url: '/res/pz/imageRepo',
    method: 'post',
    data: data
  })
}

// 修改镜像仓库
export function updateMirrorRepository(data) {
  return request({
    url: '/res/pz/imageRepo',
    method: 'put',
    data: data
  })
}

// 删除镜像仓库
export function delMirrorRepository(id) {
  return request({
    url: '/res/pz/imageRepo/' + id,
    method: 'delete'
  })
}

// 导出镜像仓库
export function exportMirrorRepository(query) {
  return request({
    url: '/res/pz/imageRepo/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/pz/imageRepo/importTemplate',
    method: 'get'
  })
}
