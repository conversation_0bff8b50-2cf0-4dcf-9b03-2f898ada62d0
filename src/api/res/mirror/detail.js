import request from '@/utils/request'

// 查询镜像明细列表
export function listMirrorDetail(query) {
  return request({
    url: '/res/image/list',
    method: 'get',
    params: query
  })
}

// 查询镜像明细详细
export function getMirrorDetail(id) {
  return request({
    url: '/res/image/' + id,
    method: 'get'
  })
}

// 新增镜像明细
export function addMirrorDetail(data) {
  return request({
    url: '/res/image',
    method: 'post',
    data: data
  })
}

// 修改镜像明细
export function updateMirrorDetail(data) {
  return request({
    url: '/res/image',
    method: 'put',
    data: data
  })
}

// 删除镜像明细
export function delMirrorDetail(id) {
  return request({
    url: '/res/image/' + id,
    method: 'delete'
  })
}

// 导出镜像明细
export function exportMirrorDetail(query) {
  return request({
    url: '/res/image/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/image/importTemplate',
    method: 'get'
  })
}
