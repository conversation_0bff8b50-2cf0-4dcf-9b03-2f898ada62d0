import request from '@/utils/request'

// 查询镜像库列表
export function listMirrorLibrary(query) {
  return request({
    url: '/risk/imageInspect/container/list',
    method: 'get',
    params: query
  })
}

// 查询镜像库详细
export function getMirrorLibrary(id) {
  return request({
    url: '/risk/imageInspect/container/' + id,
    method: 'get'
  })
}

// 新增镜像库
export function addMirrorLibrary(data) {
  return request({
    url: '/risk/imageInspect/container',
    method: 'post',
    data: data
  })
}

// 修改镜像库
export function updateMirrorLibrary(data) {
  return request({
    url: '/risk/imageInspect/container',
    method: 'put',
    data: data
  })
}

// 删除镜像库
export function delMirrorLibrary(id) {
  return request({
    url: '/risk/imageInspect/container/' + id,
    method: 'delete'
  })
}

// 导出镜像库
export function exportMirrorLibrary(query) {
  return request({
    url: '/risk/imageInspect/container/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/imageInspect/container/importTemplate',
    method: 'get'
  })
}
