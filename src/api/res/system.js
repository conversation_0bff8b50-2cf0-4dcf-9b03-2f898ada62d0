import request from '@/utils/request'

// 查询磐基业务系统列表
export function listSystem(query) {
  return request({
    url: '/res/system/page/list',
    method: 'get',
    params: query
  })
}

// 查询磐基业务系统详细
export function getSystem(id) {
  return request({
    url: '/res/system/' + id,
    method: 'get'
  })
}

// 新增磐基业务系统
export function addSystem(data) {
  return request({
    url: '/res/system',
    method: 'post',
    data: data
  })
}

// 修改磐基业务系统
export function updateSystem(data) {
  return request({
    url: '/res/system',
    method: 'put',
    data: data
  })
}

// 删除磐基业务系统
export function delSystem(id) {
  return request({
    url: '/res/system/' + id,
    method: 'delete'
  })
}

// 导出磐基业务系统
export function exportSystem(query) {
  return request({
    url: '/res/system/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/system/importTemplate',
    method: 'get'
  })
}
