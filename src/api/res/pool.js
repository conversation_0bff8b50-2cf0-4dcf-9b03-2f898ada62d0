import request from '@/utils/request'

// 查询资源池列表
export function listPool(query) {
  return request({
    url: '/res/pool/list',
    method: 'get',
    params: query
  })
}

// 查询资源池详细
export function getPool(id) {
  return request({
    url: '/res/pool/' + id,
    method: 'get'
  })
}

// 新增资源池
export function addPool(data) {
  return request({
    url: '/res/pool',
    method: 'post',
    data: data
  })
}

// 修改资源池
export function updatePool(data) {
  return request({
    url: '/res/pool',
    method: 'put',
    data: data
  })
}

// 删除资源池
export function delPool(id) {
  return request({
    url: '/res/pool/' + id,
    method: 'delete'
  })
}

// 导出资源池
export function exportPool(query) {
  return request({
    url: '/res/pool/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/pool/importTemplate',
    method: 'get'
  })
}
