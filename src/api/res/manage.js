import request from '@/utils/request'

// 查询主机巡检管理列表
export function listManage(query) {
  return request({
    url: '/res/manage/list',
    method: 'get',
    params: query
  })
}

// 查询主机巡检管理详细
export function getManage(id) {
  return request({
    url: '/res/manage/' + id,
    method: 'get'
  })
}

// 新增主机巡检管理
export function addManage(data) {
  return request({
    url: '/res/manage',
    method: 'post',
    data: data
  })
}

// 修改主机巡检管理
export function updateManage(data) {
  return request({
    url: '/res/manage',
    method: 'put',
    data: data
  })
}

// 删除主机巡检管理
export function delManage(id) {
  return request({
    url: '/res/manage/' + id,
    method: 'delete'
  })
}

// 导出主机巡检管理
export function exportManage(query) {
  return request({
    url: '/res/manage/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/manage/importTemplate',
    method: 'get'
  })
}
