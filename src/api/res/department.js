import request from '@/utils/request'

// 查询磐基业务部门列表
export function listDepartment(query) {
  return request({
    url: '/res/department/list',
    method: 'get',
    params: query
  })
}

// 查询磐基业务部门详细
export function getDepartment(id) {
  return request({
    url: '/res/department/' + id,
    method: 'get'
  })
}

// 新增磐基业务部门
export function addDepartment(data) {
  return request({
    url: '/res/department',
    method: 'post',
    data: data
  })
}

// 修改磐基业务部门
export function updateDepartment(data) {
  return request({
    url: '/res/department',
    method: 'put',
    data: data
  })
}

// 删除磐基业务部门
export function delDepartment(id) {
  return request({
    url: '/res/department/' + id,
    method: 'delete'
  })
}

// 导出磐基业务部门
export function exportDepartment(query) {
  return request({
    url: '/res/department/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/department/importTemplate',
    method: 'get'
  })
}
