import request from '@/utils/request'

// 查询磐舟代码仓库资源列表
export function listCode(query) {
  return request({
    url: '/res/code/list',
    method: 'get',
    params: query
  })
}

// 查询磐舟代码仓库资源详细
export function getCode(id) {
  return request({
    url: '/res/code/' + id,
    method: 'get'
  })
}

// 新增磐舟代码仓库资源
export function addCode(data) {
  return request({
    url: '/res/code',
    method: 'post',
    data: data
  })
}

// 修改磐舟代码仓库资源
export function updateCode(data) {
  return request({
    url: '/res/code',
    method: 'put',
    data: data
  })
}

// 删除磐舟代码仓库资源
export function delCode(id) {
  return request({
    url: '/res/code/' + id,
    method: 'delete'
  })
}

// 导出磐舟代码仓库资源
export function exportCode(query) {
  return request({
    url: '/res/code/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/code/importTemplate',
    method: 'get'
  })
}
