import request from '@/utils/request'

// 查询数据库资产管理列表
export function listDatabaseAsset(query) {
  return request({
    url: '/res/databaseAssetManage/list',
    method: 'get',
    params: query
  })
}

// 查询数据库资产管理详细
export function getDatabaseAsset(id) {
  return request({
    url: '/res/databaseAssetManage/' + id,
    method: 'get'
  })
}

// 新增数据库资产管理
export function addDatabaseAsset(data) {
  return request({
    url: '/res/databaseAssetManage',
    method: 'post',
    data: data
  })
}

// 修改数据库资产管理
export function updateDatabaseAsset(data) {
  return request({
    url: '/res/databaseAssetManage',
    method: 'put',
    data: data
  })
}

// 删除数据库资产管理
export function delDatabaseAsset(id) {
  return request({
    url: '/res/databaseAssetManage/' + id,
    method: 'delete'
  })
}

// 导出数据库资产管理
export function exportDatabaseAsset(query) {
  return request({
    url: '/res/databaseAssetManage/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/databaseAssetManage/importTemplate',
    method: 'get'
  })
}
