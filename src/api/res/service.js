import request from '@/utils/request'

// 查询磐基服务管理列表
export function listService(query) {
  return request({
    url: '/res/service/list',
    method: 'get',
    params: query
  })
}

// 查询磐基服务管理详细
export function getService(id) {
  return request({
    url: '/res/service/' + id,
    method: 'get'
  })
}

// 新增磐基服务管理
export function addService(data) {
  return request({
    url: '/res/service',
    method: 'post',
    data: data
  })
}

// 修改磐基服务管理
export function updateService(data) {
  return request({
    url: '/res/service',
    method: 'put',
    data: data
  })
}

// 删除磐基服务管理
export function delService(id) {
  return request({
    url: '/res/service/' + id,
    method: 'delete'
  })
}

// 导出磐基服务管理
export function exportService(query) {
  return request({
    url: '/res/service/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/service/importTemplate',
    method: 'get'
  })
}
