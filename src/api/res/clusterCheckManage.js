import request from '@/utils/request'

// 查询集群检查管理信息列表
export function listManage(query) {
  return request({
    url: '/res/clusterManage/list',
    method: 'get',
    params: query
  })
}

// 查询集群检查管理信息详细
export function getManage(id) {
  return request({
    url: '/res/clusterManage/' + id,
    method: 'get'
  })
}

// 新增集群检查管理信息
export function addManage(data) {
  return request({
    url: '/res/clusterManage',
    method: 'post',
    data: data
  })
}

// 修改集群检查管理信息
export function updateManage(data) {
  return request({
    url: '/res/clusterManage',
    method: 'put',
    data: data
  })
}

// 删除集群检查管理信息
export function delManage(id) {
  return request({
    url: '/res/clusterManage/' + id,
    method: 'delete'
  })
}

// 导出集群检查管理信息
export function exportManage(query) {
  return request({
    url: '/res/clusterManage/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/clusterManage/importTemplate',
    method: 'get'
  })
}
