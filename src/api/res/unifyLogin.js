import request from '@/utils/request'

// 查询统一登录资源列表
export function listUnifyLogin(query) {
  return request({
    url: '/res/unifyLoginResources/list',
    method: 'get',
    params: query
  })
}

// 查询统一登录资源详细
export function getUnifyLogin(id) {
  return request({
    url: '/res/unifyLoginResources/' + id,
    method: 'get'
  })
}

// 新增统一登录资源
export function addUnifyLogin(data) {
  return request({
    url: '/res/unifyLoginResources',
    method: 'post',
    data: data
  })
}

// 修改统一登录资源
export function updateUnifyLogin(data) {
  return request({
    url: '/res/unifyLoginResources',
    method: 'put',
    data: data
  })
}

// 删除统一登录资源
export function delUnifyLogin(id) {
  return request({
    url: '/res/unifyLoginResources/' + id,
    method: 'delete'
  })
}

// 导出统一登录资源
export function exportUnifyLogin(query) {
  return request({
    url: '/res/unifyLoginResources/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/unifyLoginResources/importTemplate',
    method: 'get'
  })
}

/** 查询登录资源绑定已绑定的用户列表 */
export function resUnifyLoginResourcesQueryBindUserList(params) {
  return request({
    url: '/res/unifyLoginResources/queryBindUserList',
    method: 'get',
    params
  })
}

/** 查询登录资源绑定可以绑定的用户列表 */
export function resUnifyLoginResourcesQueryUserList(params) {
  return request({
    url: '/res/unifyLoginResources/queryUserList',
    method: 'get',
    params
  })
}

/** 绑定登录资源与从账户的关联 */
export function resUnifyLoginResourcesSaveBindUser(data) {
  return request({
    url: '/res/unifyLoginResources/saveBindUser',
    method: 'post',
    data
  })
}

/** 删除登录资源与从账户的关联 */
export function resUnifyLoginResourcesDelBindUser(data = {}) {
  return request({
    url: '/res/unifyLoginResources/delBindUser',
    method: 'post',
    data
  })
}

/** 查询当前用户可跳转登录资源列表 */
export function resUnifyLoginResourcesQuerySourceList(data) {
  return request({
    url: '/res/unifyLoginResources/querySourceList',
    method: 'post',
    data
  })
}
