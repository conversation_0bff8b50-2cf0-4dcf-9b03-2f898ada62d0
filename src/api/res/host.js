import request from '@/utils/request'

// 查询磐基资源主机列表
export function listHost(query) {
  return request({
    url: '/res/host/list',
    method: 'get',
    params: query
  })
}

// 查询磐基资源主机详细
export function getHost(id) {
  return request({
    url: '/res/host/' + id,
    method: 'get'
  })
}

// 新增磐基资源主机
export function addHost(data) {
  return request({
    url: '/res/host',
    method: 'post',
    data: data
  })
}

// 修改磐基资源主机
export function updateHost(data) {
  return request({
    url: '/res/host',
    method: 'put',
    data: data
  })
}

// 删除磐基资源主机
export function delHost(id) {
  return request({
    url: '/res/host/' + id,
    method: 'delete'
  })
}

// 导出磐基资源主机
export function exportHost(query) {
  return request({
    url: '/res/host/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/host/importTemplate',
    method: 'get'
  })
}

/** 获取变更流水记录 */
export function resHostFlow(id) {
  return request({
    url: `/res/host/flow/${id}`,
    method: 'get'
  })
}
