import request from '@/utils/request'

// 查询磐基服务组件列表
export function listComponents(query) {
  return request({
    url: '/res/components/list',
    method: 'get',
    params: query
  })
}

// 查询磐基服务组件详细
export function getComponents(id) {
  return request({
    url: '/res/components/' + id,
    method: 'get'
  })
}

// 新增磐基服务组件
export function addComponents(data) {
  return request({
    url: '/res/components',
    method: 'post',
    data: data
  })
}

// 修改磐基服务组件
export function updateComponents(data) {
  return request({
    url: '/res/components',
    method: 'put',
    data: data
  })
}

// 删除磐基服务组件
export function delComponents(id) {
  return request({
    url: '/res/components/' + id,
    method: 'delete'
  })
}

// 导出磐基服务组件
export function exportComponents(query) {
  return request({
    url: '/res/components/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/components/importTemplate',
    method: 'get'
  })
}
