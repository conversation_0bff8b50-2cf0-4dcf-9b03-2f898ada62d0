import request from '@/utils/request'

// 查询节点资源列表
export function listNode(query) {
  return request({
    url: '/res/node/list',
    method: 'get',
    params: query
  })
}

// 查询节点资源详细
export function getNode(id) {
  return request({
    url: '/res/node/' + id,
    method: 'get'
  })
}

// 新增节点资源
export function addNode(data) {
  return request({
    url: '/res/node',
    method: 'post',
    data: data
  })
}

// 修改节点资源
export function updateNode(data) {
  return request({
    url: '/res/node',
    method: 'put',
    data: data
  })
}

// 删除节点资源
export function delNode(id) {
  return request({
    url: '/res/node/' + id,
    method: 'delete'
  })
}

// 导出节点资源
export function exportNode(query) {
  return request({
    url: '/res/node/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/node/importTemplate',
    method: 'get'
  })
}
