import request from '@/utils/request'

// 查询磐舟镜像库资源列表
export function listImage(query) {
  return request({
    url: '/res/image/list',
    method: 'get',
    params: query
  })
}

// 查询磐舟镜像库资源详细
export function getImage(id) {
  return request({
    url: '/res/image/' + id,
    method: 'get'
  })
}

// 新增磐舟镜像库资源
export function addImage(data) {
  return request({
    url: '/res/image',
    method: 'post',
    data: data
  })
}

// 修改磐舟镜像库资源
export function updateImage(data) {
  return request({
    url: '/res/image',
    method: 'put',
    data: data
  })
}

// 删除磐舟镜像库资源
export function delImage(id) {
  return request({
    url: '/res/image/' + id,
    method: 'delete'
  })
}

// 导出磐舟镜像库资源
export function exportImage(query) {
  return request({
    url: '/res/image/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/image/importTemplate',
    method: 'get'
  })
}
