import request from '@/utils/request'

// 查询磐舟租户资源列表
export function listTenant(query) {
  return request({
    url: '/res/pzTenant/list',
    method: 'get',
    params: query
  })
}

// 查询磐舟租户资源详细
export function getTenant(id) {
  return request({
    url: '/res/pzTenant/' + id,
    method: 'get'
  })
}

// 新增磐舟租户资源
export function addTenant(data) {
  return request({
    url: '/res/pzTenant',
    method: 'post',
    data: data
  })
}

// 修改磐舟租户资源
export function updateTenant(data) {
  return request({
    url: '/res/pzTenant',
    method: 'put',
    data: data
  })
}

// 删除磐舟租户资源
export function delTenant(id) {
  return request({
    url: '/res/pzTenant/' + id,
    method: 'delete'
  })
}

// 导出磐舟租户资源
export function exportTenant(query) {
  return request({
    url: '/res/pzTenant/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/pzTenant/importTemplate',
    method: 'get'
  })
}
