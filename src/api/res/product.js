import request from '@/utils/request'

// 查询磐舟制品库资源列表
export function listProduct(query) {
  return request({
    url: '/res/product/list',
    method: 'get',
    params: query
  })
}

// 查询磐舟制品库资源详细
export function getProduct(id) {
  return request({
    url: '/res/product/' + id,
    method: 'get'
  })
}

// 新增磐舟制品库资源
export function addProduct(data) {
  return request({
    url: '/res/product',
    method: 'post',
    data: data
  })
}

// 修改磐舟制品库资源
export function updateProduct(data) {
  return request({
    url: '/res/product',
    method: 'put',
    data: data
  })
}

// 删除磐舟制品库资源
export function delProduct(id) {
  return request({
    url: '/res/product/' + id,
    method: 'delete'
  })
}

// 导出磐舟制品库资源
export function exportProduct(query) {
  return request({
    url: '/res/product/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/product/importTemplate',
    method: 'get'
  })
}
