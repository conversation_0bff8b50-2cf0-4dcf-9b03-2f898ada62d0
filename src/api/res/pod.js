import request from '@/utils/request'

// 查询磐基资源POD列表
export function listPod(query) {
  return request({
    url: '/res/pod/list',
    method: 'get',
    params: query
  })
}

// 查询磐基资源POD详细
export function getPod(id) {
  return request({
    url: '/res/pod/' + id,
    method: 'get'
  })
}

// 新增磐基资源POD
export function addPod(data) {
  return request({
    url: '/res/pod',
    method: 'post',
    data: data
  })
}

// 修改磐基资源POD
export function updatePod(data) {
  return request({
    url: '/res/pod',
    method: 'put',
    data: data
  })
}

// 删除磐基资源POD
export function delPod(id) {
  return request({
    url: '/res/pod/' + id,
    method: 'delete'
  })
}

// 导出磐基资源POD
export function exportPod(query) {
  return request({
    url: '/res/pod/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/pod/importTemplate',
    method: 'get'
  })
}
