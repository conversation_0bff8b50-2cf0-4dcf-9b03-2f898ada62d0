import request from '@/utils/request'

// 查询业务模块资源列表
export function listModule(query) {
  return request({
    url: '/res/module/list',
    method: 'get',
    params: query
  })
}

// 查询业务模块资源详细
export function getModule(id) {
  return request({
    url: '/res/module/' + id,
    method: 'get'
  })
}

// 新增业务模块资源
export function addModule(data) {
  return request({
    url: '/res/module',
    method: 'post',
    data: data
  })
}

// 修改业务模块资源
export function updateModule(data) {
  return request({
    url: '/res/module',
    method: 'put',
    data: data
  })
}

// 删除业务模块资源
export function delModule(id) {
  return request({
    url: '/res/module/' + id,
    method: 'delete'
  })
}

// 导出业务模块资源
export function exportModule(query) {
  return request({
    url: '/res/module/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/module/importTemplate',
    method: 'get'
  })
}
