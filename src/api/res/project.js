import request from '@/utils/request'

// 查询磐舟项目信息资源列表
export function listProject(query) {
  return request({
    url: '/res/project/list',
    method: 'get',
    params: query
  })
}

// 查询磐舟项目信息资源详细
export function getProject(id) {
  return request({
    url: '/res/project/' + id,
    method: 'get'
  })
}

// 新增磐舟项目信息资源
export function addProject(data) {
  return request({
    url: '/res/project',
    method: 'post',
    data: data
  })
}

// 修改磐舟项目信息资源
export function updateProject(data) {
  return request({
    url: '/res/project',
    method: 'put',
    data: data
  })
}

// 删除磐舟项目信息资源
export function delProject(id) {
  return request({
    url: '/res/project/' + id,
    method: 'delete'
  })
}

// 导出磐舟项目信息资源
export function exportProject(query) {
  return request({
    url: '/res/project/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/project/importTemplate',
    method: 'get'
  })
}
