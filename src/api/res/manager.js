import request from '@/utils/request'

// 查询磐基Pod管理列表
export function listManager(query) {
  return request({
    url: '/res/k8sPod/list',
    method: 'get',
    params: query
  })
}

// 查询磐基Pod管理详细
export function getManager(id) {
  return request({
    url: '/res/k8sPod/' + id,
    method: 'get'
  })
}

// 新增磐基Pod管理
export function addManager(data) {
  return request({
    url: '/res/manager',
    method: 'post',
    data: data
  })
}

// 修改磐基Pod管理
export function updateManager(data) {
  return request({
    url: '/res/manager',
    method: 'put',
    data: data
  })
}

// 删除磐基Pod管理
export function delManager(id) {
  return request({
    url: '/res/manager/' + id,
    method: 'delete'
  })
}

// 导出磐基Pod管理
export function exportManager(query) {
  return request({
    url: '/res/k8sPod/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/manager/importTemplate',
    method: 'get'
  })
}
