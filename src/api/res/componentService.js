import request from '@/utils/request'

// 查询磐基服务组件列表
export function listComponentService(query) {
  return request({
    url: '/res/componentService/list',
    method: 'get',
    params: query
  })
}

// 查询磐基服务组件详细
export function getComponentService(id) {
  return request({
    url: '/res/componentService/' + id,
    method: 'get'
  })
}

// 新增磐基服务组件
export function addComponentService(data) {
  return request({
    url: '/res/componentService',
    method: 'post',
    data: data
  })
}

// 修改磐基服务组件
export function updateComponentService(data) {
  return request({
    url: '/res/componentService',
    method: 'put',
    data: data
  })
}

// 删除磐基服务组件
export function delComponentService(id) {
  return request({
    url: '/res/componentService/' + id,
    method: 'delete'
  })
}

// 导出磐基服务组件
export function exportComponentService(query) {
  return request({
    url: '/res/componentService/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/componentService/importTemplate',
    method: 'get'
  })
}
