import request from '@/utils/request'

// 查询主机巡检列表
export function listCheck(query) {
  return request({
    url: '/res/check/list',
    method: 'get',
    params: query
  })
}

// 查询主机巡检详细
export function getCheck(id) {
  return request({
    url: '/res/check/' + id,
    method: 'get'
  })
}

// 新增主机巡检
export function addCheck(data) {
  return request({
    url: '/res/check',
    method: 'post',
    data: data
  })
}

// 修改主机巡检
export function updateCheck(data) {
  return request({
    url: '/res/check',
    method: 'put',
    data: data
  })
}

// 删除主机巡检
export function delCheck(id) {
  return request({
    url: '/res/check/' + id,
    method: 'delete'
  })
}

// 导出主机巡检
export function exportCheck(query) {
  return request({
    url: '/res/check/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/check/importTemplate',
    method: 'get'
  })
}
