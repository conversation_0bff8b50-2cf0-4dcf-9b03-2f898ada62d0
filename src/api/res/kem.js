import request from '@/utils/request'

// 查询KEM资源列表
export function listKem(query) {
  return request({
    url: '/res/kem/list',
    method: 'get',
    params: query
  })
}

// 查询KEM资源详细
export function getKem(id) {
  return request({
    url: '/res/kem/' + id,
    method: 'get'
  })
}

// 新增KEM资源
export function addKem(data) {
  return request({
    url: '/res/kem',
    method: 'post',
    data: data
  })
}

// 修改KEM资源
export function updateKem(data) {
  return request({
    url: '/res/kem',
    method: 'put',
    data: data
  })
}

// 删除KEM资源
export function delKem(id) {
  return request({
    url: '/res/kem/' + id,
    method: 'delete'
  })
}

// 导出KEM资源
export function exportKem(query) {
  return request({
    url: '/res/kem/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/kem/importTemplate',
    method: 'get'
  })
}
