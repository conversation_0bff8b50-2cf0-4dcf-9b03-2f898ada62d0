import request from '@/utils/request'

// 查询集群资源列表
export function listCluster(query) {
  return request({
    url: '/res/cluster/list',
    method: 'get',
    params: query
  })
}

// 查询集群资源详细
export function getCluster(id) {
  return request({
    url: '/res/cluster/' + id,
    method: 'get'
  })
}

// 新增集群资源
export function addCluster(data) {
  return request({
    url: '/res/cluster',
    method: 'post',
    data: data
  })
}

// 修改集群资源
export function updateCluster(data) {
  return request({
    url: '/res/cluster',
    method: 'put',
    data: data
  })
}

// 删除集群资源
export function delCluster(id) {
  return request({
    url: '/res/cluster/' + id,
    method: 'delete'
  })
}

// 导出集群资源
export function exportCluster(query) {
  return request({
    url: '/res/cluster/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/cluster/importTemplate',
    method: 'get'
  })
}
