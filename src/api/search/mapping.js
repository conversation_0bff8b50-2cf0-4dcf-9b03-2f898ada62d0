import request from '@/utils/request'

// 查询安全问题ES字段映射列表
export function listMapping(query) {
  return request({
    url: '/risk/search/mapping/list',
    method: 'get',
    params: query
  })
}

// 查询安全问题ES字段映射详细
export function getMapping(id) {
  return request({
    url: '/risk/search/mapping/' + id,
    method: 'get'
  })
}

// 新增安全问题ES字段映射
export function addMapping(data) {
  return request({
    url: '/risk/search/mapping',
    method: 'post',
    data: data
  })
}

// 修改安全问题ES字段映射
export function updateMapping(data) {
  return request({
    url: '/risk/search/mapping',
    method: 'put',
    data: data
  })
}

// 删除安全问题ES字段映射
export function delMapping(id) {
  return request({
    url: '/risk/search/mapping/' + id,
    method: 'delete'
  })
}

// 导出安全问题ES字段映射
export function exportMapping(query) {
  return request({
    url: '/risk/search/mapping/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/search/mapping/importTemplate',
    method: 'get'
  })
}
