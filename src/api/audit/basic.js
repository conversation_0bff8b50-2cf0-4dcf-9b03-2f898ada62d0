import request from '@/utils/request'

// 查询安全审计待办列表
export function listBusiness(query) {
  return request({
    url: '/risk/business/list',
    method: 'get',
    params: query
  })
}

// 查询安全审计待办详情
export function getBusiness(id) {
  return request({
    url: '/risk/business/' + id,
    method: 'get'
  })
}

// 查询安全审计映射列表
export function riskMappingGetMappedlist(query) {
  return request({
    url: '/risk/mapping/getMappedlist',
    method: 'get',
    params: query
  })
}

/** 查询大项或小项列表 */
export function riskSecurityAuditGetDistinctClassify(params) {
  return request({
    url: '/risk/securityAudit/getDistinctClassify',
    method: 'get',
    params
  })
}
