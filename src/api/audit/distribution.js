import request from '@/utils/request'

// 查询业务分配规则列表
export function listRule(query) {
  return request({
    url: '/risk/rule/list',
    method: 'get',
    params: query
  })
}

// 查询业务分配规则详细
export function getRule(id) {
  return request({
    url: '/risk/rule/' + id,
    method: 'get'
  })
}

// 新增业务分配规则
export function addRule(data) {
  return request({
    url: '/risk/rule',
    method: 'post',
    data: data
  })
}

// 修改业务分配规则
export function updateRule(data) {
  return request({
    url: '/risk/rule',
    method: 'put',
    data: data
  })
}

// 删除业务分配规则
export function delRule(id, data) {
  return request({
    url: '/risk/rule/' + id,
    method: 'delete',
    data
  })
}

// 导出业务分配规则
export function exportRule(query) {
  return request({
    url: '/risk/rule/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/rule/importTemplate',
    method: 'get'
  })
}

// 查询审计类树数据
export function riskSecurityAuditTree(data) {
  return request({
    url: '/risk/securityAudit/tree',
    method: 'get',
    params: data
  })
}
