import request from '@/utils/request'

// 查询安全审计列表
export function listSecurityAudit(query) {
  return request({
    url: '/risk/securityAudit/list',
    method: 'get',
    params: query
  })
}

// 查询安全审计详细
export function getSecurityAudit(id) {
  return request({
    url: '/risk/securityAudit/' + id,
    method: 'get'
  })
}

// 新增安全审计
export function addSecurityAudit(data) {
  return request({
    url: '/risk/securityAudit',
    method: 'post',
    data: data
  })
}

// 修改安全审计
export function updateSecurityAudit(data) {
  return request({
    url: '/risk/securityAudit',
    method: 'put',
    data: data
  })
}

// 删除安全审计
export function delSecurityAudit(id) {
  return request({
    url: '/risk/securityAudit/' + id,
    method: 'delete'
  })
}

// 导出安全审计
export function exportSecurityAudit(query) {
  return request({
    url: '/risk/securityAudit/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/securityAudit/importTemplate',
    method: 'get'
  })
}
