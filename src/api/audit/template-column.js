import request from '@/utils/request'

// 查询安全审计列表
export function listSecurityAuditColumn(query) {
  return request({
    url: '/risk/securityAuditColumn/list',
    method: 'get',
    params: query
  })
}

// 查询安全审计详细
export function getSecurityAuditColumn(id) {
  return request({
    url: '/risk/securityAuditColumn/' + id,
    method: 'get'
  })
}

// 新增安全审计
export function addSecurityAuditColumn(data) {
  return request({
    url: '/risk/securityAuditColumn',
    method: 'post',
    data: data
  })
}

// 修改安全审计
export function updateSecurityAuditColumn(data) {
  return request({
    url: '/risk/securityAuditColumn',
    method: 'put',
    data: data
  })
}

// 删除安全审计
export function delSecurityAuditColumn(id) {
  return request({
    url: '/risk/securityAuditColumn/' + id,
    method: 'delete'
  })
}

// 导出安全审计
export function exportSecurityAuditColumn(query) {
  return request({
    url: '/risk/securityAuditColumn/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/securityAuditColumn/importTemplate',
    method: 'get'
  })
}
