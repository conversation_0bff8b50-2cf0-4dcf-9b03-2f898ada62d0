import request from '@/utils/request'

// 查询镜像信息列表
export function listInfo(query) {
  return request({
    url: '/ima/info/list',
    method: 'get',
    params: query,
    silent: true
  })
}

// 查询镜像信息详细
export function getInfo(id) {
  return request({
    url: '/ima/info/' + id,
    method: 'get'
  })
}

// 新增镜像信息
export function addInfo(data) {
  return request({
    url: '/ima/info',
    method: 'post',
    data: data
  })
}

// 修改镜像信息
export function updateInfo(data) {
  return request({
    url: '/ima/info',
    method: 'put',
    data: data
  })
}

// 删除镜像信息
export function delInfo(id) {
  return request({
    url: '/ima/info/' + id,
    method: 'delete'
  })
}

// 导出镜像信息
export function exportInfo(query) {
  return request({
    url: '/ima/info/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/info/importTemplate',
    method: 'get'
  })
}
