import request from '@/utils/request'

// 查询台账任务列表
export function listBook(query) {
  return request({
    url: '/ima/book/list',
    method: 'get',
    params: query
  })
}

// 查询台账任务详细
export function getBook(id) {
  return request({
    url: '/ima/book/' + id,
    method: 'get'
  })
}

// 新增台账任务
export function addBook(data) {
  return request({
    url: '/ima/book',
    method: 'post',
    data: data
  })
}

// 修改台账任务
export function updateBook(data) {
  return request({
    url: '/ima/book',
    method: 'put',
    data: data
  })
}

// 删除台账任务
export function delBook(id) {
  return request({
    url: '/ima/book/' + id,
    method: 'delete'
  })
}

// 导出台账任务
export function exportBook(query) {
  return request({
    url: '/ima/book/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/book/importTemplate',
    method: 'get'
  })
}
