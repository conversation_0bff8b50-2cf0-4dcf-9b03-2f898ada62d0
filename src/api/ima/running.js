import request from '@/utils/request'

// 查询镜像排查原始数据(磐舟)列表
export function listPz(query) {
  return request({
    url: '/risk/imageInspect/pz/list',
    method: 'get',
    params: query
  })
}

// 查询镜像排查原始数据(磐舟)详细
export function getPz(id) {
  return request({
    url: '/risk/imageInspect/pz/' + id,
    method: 'get'
  })
}

// 新增镜像排查原始数据(磐舟)
export function addPz(data) {
  return request({
    url: '/risk/imageInspect/pz',
    method: 'post',
    data: data
  })
}

// 修改镜像排查原始数据(磐舟)
export function updatePz(data) {
  return request({
    url: '/risk/imageInspect/pz',
    method: 'put',
    data: data
  })
}

// 删除镜像排查原始数据(磐舟)
export function delPz(id) {
  return request({
    url: '/risk/imageInspect/pz/' + id,
    method: 'delete'
  })
}

// 导出镜像排查原始数据(磐舟)
export function exportPz(query) {
  return request({
    url: '/risk/imageInspect/pz/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/imageInspect/pz/importTemplate',
    method: 'get'
  })
}
