import request from '@/utils/request'

// 查询台账与任务管理列表
export function listBookTask(query) {
  return request({
    url: '/ima/bookTask/list',
    method: 'get',
    params: query
  })
}

// 查询台账与任务管理详细
export function getBookTask(id) {
  return request({
    url: '/ima/bookTask/' + id,
    method: 'get'
  })
}

// 新增台账与任务管理
export function addBookTask(data) {
  return request({
    url: '/ima/bookTask',
    method: 'post',
    data: data
  })
}

// 修改台账与任务管理
export function updateBookTask(data) {
  return request({
    url: '/ima/bookTask',
    method: 'put',
    data: data
  })
}

// 删除台账与任务管理
export function delBookTask(id) {
  return request({
    url: '/ima/bookTask/' + id,
    method: 'delete'
  })
}

// 导出台账与任务管理
export function exportBookTask(query) {
  return request({
    url: '/ima/bookTask/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/bookTask/importTemplate',
    method: 'get'
  })
}
