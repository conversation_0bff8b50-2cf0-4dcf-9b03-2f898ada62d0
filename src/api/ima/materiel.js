import request from '@/utils/request'

// 查询镜像排查原始数据(磐舟)列表
export function listMateriel(query) {
  return request({
    url: '/risk/imageMaterial/list',
    method: 'get',
    params: query
  })
}

// 查询镜像排查原始数据(磐舟)详细
export function getMateriel(id) {
  return request({
    url: '/risk/imageMaterial/' + id,
    method: 'get'
  })
}

// 新增镜像排查原始数据(磐舟)
export function addMateriel(data) {
  return request({
    url: '/risk/imageMaterial',
    method: 'post',
    data: data
  })
}

// 修改镜像排查原始数据(磐舟)
export function updateMateriel(data) {
  return request({
    url: '/risk/imageMaterial',
    method: 'put',
    data: data
  })
}

// 删除镜像排查原始数据(磐舟)
export function delMateriel(id) {
  return request({
    url: '/risk/imageMaterial/' + id,
    method: 'delete'
  })
}

// 导出镜像排查原始数据(磐舟)
export function exportMateriel(query) {
  return request({
    url: '/risk/imageMaterial/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/imageMaterial/importTemplate',
    method: 'get'
  })
}
