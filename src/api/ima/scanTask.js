import request from '@/utils/request'

// 查询镜像扫描任务列表
export function listScanTask(query) {
  return request({
    url: '/ima/scanTask/list',
    method: 'get',
    params: query
  })
}

// 查询镜像扫描任务详细
export function getScanTask(id) {
  return request({
    url: '/ima/scanTask/' + id,
    method: 'get'
  })
}

// 新增镜像扫描任务
export function addScanTask(data) {
  return request({
    url: '/ima/scanTask',
    method: 'post',
    data: data
  })
}

// 修改镜像扫描任务
export function updateScanTask(data) {
  return request({
    url: '/ima/scanTask',
    method: 'put',
    data: data
  })
}

// 删除镜像扫描任务
export function delScanTask(id) {
  return request({
    url: '/ima/scanTask/' + id,
    method: 'delete'
  })
}

// 导出镜像扫描任务
export function exportScanTask(query) {
  return request({
    url: '/ima/scanTask/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/scanTask/importTemplate',
    method: 'get'
  })
}
