import request from '@/utils/request'

// 查询镜像扫描流水详情列表
export function listScanTaskDetail(query) {
  return request({
    url: '/ima/scanTaskDetail/list',
    method: 'get',
    params: query,
    silent: true
  })
}

// 查询镜像扫描流水详情详细
export function getScanTaskDetail(id) {
  return request({
    url: '/ima/scanTaskDetail/' + id,
    method: 'get'
  })
}

// 新增镜像扫描流水详情
export function addScanTaskDetail(data) {
  return request({
    url: '/ima/scanTaskDetail',
    method: 'post',
    data: data
  })
}

// 修改镜像扫描流水详情
export function updateScanTaskDetail(data) {
  return request({
    url: '/ima/scanTaskDetail',
    method: 'put',
    data: data
  })
}

// 删除镜像扫描流水详情
export function delScanTaskDetail(id) {
  return request({
    url: '/ima/scanTaskDetail/' + id,
    method: 'delete'
  })
}

// 导出镜像扫描流水详情
export function exportScanTaskDetail(query) {
  return request({
    url: '/ima/scanTaskDetail/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/scanTaskDetail/importTemplate',
    method: 'get'
  })
}

/** 构建并扫描 批量 */
export function imaScanTaskDetailBatchSan(params = {}) {
  return request({
    url: '/ima/scanTaskDetail/batch/san',
    method: 'get',
    params
  })
}

/** 构建并扫描 单个 */
export function imaScanTaskDetailBuildSan(params = {}) {
  return request({
    url: '/ima/scanTaskDetail/build/san',
    method: 'get',
    params
  })
}

/** 扫描记录列表 */
export function imaScanTaskDetailScaScanHistory(params = {}) {
  return request({
    url: '/ima/scanTaskDetail/scaScan/history',
    method: 'get',
    params
  })
}
