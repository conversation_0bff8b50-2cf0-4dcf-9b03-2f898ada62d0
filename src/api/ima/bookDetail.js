import request from '@/utils/request'

// 查询台账列表
export function listBookDetail(query) {
  return request({
    url: '/ima/book/standingBookList',
    method: 'get',
    params: query
  })
}

// 查询台账详细
export function getBookDetail(id) {
  return request({
    url: '/ima/bookDetail/' + id,
    method: 'get'
  })
}

// 新增台账
export function addBookDetail(data) {
  return request({
    url: '/ima/bookDetail',
    method: 'post',
    data: data
  })
}

// 修改台账
export function updateBookDetail(data) {
  return request({
    url: '/ima/bookDetail',
    method: 'put',
    data: data
  })
}

// 删除台账
export function delBookDetail(id) {
  return request({
    url: '/ima/bookDetail/' + id,
    method: 'delete'
  })
}

// 导出台账
export function exportBookDetail(query) {
  return request({
    url: '/ima/bookDetail/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/bookDetail/importTemplate',
    method: 'get'
  })
}
