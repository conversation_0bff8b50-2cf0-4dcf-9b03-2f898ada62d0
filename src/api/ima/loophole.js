import request from '@/utils/request'

// 查询镜像漏洞管理列表
export function listLoophole(query) {
  return request({
    url: '/ima/loophole/list',
    method: 'get',
    params: query
  })
}

// 查询镜像漏洞管理详细
export function getLoophole(id) {
  return request({
    url: '/ima/loophole/' + id,
    method: 'get'
  })
}

// 新增镜像漏洞管理
export function addLoophole(data) {
  return request({
    url: '/ima/loophole',
    method: 'post',
    data: data
  })
}

// 修改镜像漏洞管理
export function updateLoophole(data) {
  return request({
    url: '/ima/loophole',
    method: 'put',
    data: data
  })
}

// 删除镜像漏洞管理
export function delLoophole(id) {
  return request({
    url: '/ima/loophole/' + id,
    method: 'delete'
  })
}

// 导出镜像漏洞管理
export function exportLoophole(query) {
  return request({
    url: '/ima/loophole/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/ima/loophole/importTemplate',
    method: 'get'
  })
}

// 漏洞处置
export function riskLoopholeDispose(id) {
  return request({
    url: `/risk/loophole/dispose/${id}`,
    method: 'get'
  })
}

// 漏洞处置 批量
export function riskLoopholeDisposeBatch(ids) {
  return request({
    url: `/risk/loophole/disposeBatch/${ids}`,
    method: 'get'
  })
}
