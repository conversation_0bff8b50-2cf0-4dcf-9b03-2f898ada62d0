import request from '@/utils/request'

// 查询集群信息维护列表
export function listMaintenance(query) {
  return request({
    url: '/know/maintenance/list',
    method: 'get',
    params: query
  })
}

// 查询集群信息维护详细
export function getMaintenance(id) {
  return request({
    url: '/know/maintenance/' + id,
    method: 'get'
  })
}

// 新增集群信息维护
export function addMaintenance(data) {
  return request({
    url: '/know/maintenance',
    method: 'post',
    data: data
  })
}

// 修改集群信息维护
export function updateMaintenance(data) {
  return request({
    url: '/know/maintenance',
    method: 'put',
    data: data
  })
}

// 删除集群信息维护
export function delMaintenance(id) {
  return request({
    url: '/know/maintenance/' + id,
    method: 'delete'
  })
}

// 导出集群信息维护
export function exportMaintenance(query) {
  return request({
    url: '/know/maintenance/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/maintenance/importTemplate',
    method: 'get'
  })
}
