import request from '@/utils/request'

// 查询告警策略列表
export function listAlarmStrategy(query) {
  return request({
    url: '/res/strategy/list',
    method: 'get',
    params: query
  })
}

// 查询告警策略详细
export function getAlarmStrategy(id) {
  return request({
    url: '/res/strategy/' + id,
    method: 'get'
  })
}

// 新增告警策略
export function addAlarmStrategy(data) {
  return request({
    url: '/res/strategy',
    method: 'post',
    data: data
  })
}

// 修改告警策略
export function updateAlarmStrategy(data) {
  return request({
    url: '/res/strategy',
    method: 'put',
    data: data
  })
}

// 删除告警策略
export function delAlarmStrategy(id) {
  return request({
    url: '/res/strategy/' + id,
    method: 'delete'
  })
}

// 导出告警策略
export function exportAlarmStrategy(query) {
  return request({
    url: '/res/strategy/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/strategy/importTemplate',
    method: 'get'
  })
}
