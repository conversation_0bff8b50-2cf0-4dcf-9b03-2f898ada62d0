import request from '@/utils/request'

// 查询告警明细列表
export function listAlarmDetail(query) {
  return request({
    url: '/res/detail/list',
    method: 'get',
    params: query
  })
}

// 查询告警明细详细
export function getAlarmDetail(id) {
  return request({
    url: '/res/detail/' + id,
    method: 'get'
  })
}

// 新增告警明细
export function addAlarmDetail(data) {
  return request({
    url: '/res/detail',
    method: 'post',
    data: data
  })
}

// 修改告警明细
export function updateAlarmDetail(data) {
  return request({
    url: '/res/detail',
    method: 'put',
    data: data
  })
}

// 删除告警明细
export function delAlarmDetail(id) {
  return request({
    url: '/res/detail/' + id,
    method: 'delete'
  })
}

// 导出告警明细
export function exportAlarmDetail(query) {
  return request({
    url: '/res/detail/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/detail/importTemplate',
    method: 'get'
  })
}
