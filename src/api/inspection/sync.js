import request from '@/utils/request'

// 查询采集的巡检信息列表
export function listGatherIndex(query) {
  return request({
    url: '/gather/index/list',
    method: 'get',
    params: query
  })
}

// 查询采集的巡检信息详细
export function getGatherIndex(id) {
  return request({
    url: '/gather/index/' + id,
    method: 'get'
  })
}

// 新增采集的巡检信息
export function addGatherIndex(data) {
  return request({
    url: '/gather/index',
    method: 'post',
    data: data
  })
}

// 修改采集的巡检信息
export function updateGatherIndex(data) {
  return request({
    url: '/gather/index',
    method: 'put',
    data: data
  })
}

// 删除采集的巡检信息
export function delGatherIndex(id) {
  return request({
    url: '/gather/index/' + id,
    method: 'delete'
  })
}

// 导出采集的巡检信息
export function exportGatherIndex(query) {
  return request({
    url: '/gather/index/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/gather/index/importTemplate',
    method: 'get'
  })
}
