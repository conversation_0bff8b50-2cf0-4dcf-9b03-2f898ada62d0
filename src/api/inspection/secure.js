import request from '@/utils/request'

// 查询采集的巡检信息列表
export function listSecureDetail(query) {
  return request({
    url: '/risk/detail/secure/list',
    method: 'get',
    params: query
  })
}

// 查询采集的巡检信息详细
export function getSecureDetail(id) {
  return request({
    url: '/risk/detail/secure/' + id,
    method: 'get'
  })
}

// 新增采集的巡检信息
export function addSecureDetail(data) {
  return request({
    url: '/risk/detail/secure',
    method: 'post',
    data: data
  })
}

// 修改采集的巡检信息
export function updateSecureDetail(data) {
  return request({
    url: '/risk/detail/secure',
    method: 'put',
    data: data
  })
}

// 删除采集的巡检信息
export function delSecureDetail(id) {
  return request({
    url: '/risk/detail/secure/' + id,
    method: 'delete'
  })
}

// 导出采集的巡检信息
export function exportSecureDetail(query) {
  return request({
    url: '/risk/detail/secure/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/detail/secure/importTemplate',
    method: 'get'
  })
}
