import request from '@/utils/request'

// 查询安全设备web界面访问信息列表
export function listWebAccess(query) {
  return request({
    url: '/res/web/access/list',
    method: 'get',
    params: query
  })
}

// 查询安全设备web界面访问信息详细
export function getWebAccess(id) {
  return request({
    url: '/res/web/access/' + id,
    method: 'get'
  })
}

// 新增安全设备web界面访问信息
export function addWebAccess(data) {
  return request({
    url: '/res/web/access',
    method: 'post',
    data: data
  })
}

// 修改安全设备web界面访问信息
export function updateWebAccess(data) {
  return request({
    url: '/res/web/access',
    method: 'put',
    data: data
  })
}

// 删除安全设备web界面访问信息
export function delWebAccess(id) {
  return request({
    url: '/res/web/access/' + id,
    method: 'delete'
  })
}

// 导出安全设备web界面访问信息
export function exportWebAccess(query) {
  return request({
    url: '/res/web/access/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/res/web/access/importTemplate',
    method: 'get'
  })
}
