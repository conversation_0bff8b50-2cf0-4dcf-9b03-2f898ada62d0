import request from '@/utils/request'

// 查询检查点列表
export function listPoint(query) {
  return request({
    url: '/rule/point/list',
    method: 'get',
    params: query
  })
}

// 查询检查点（精简)列表
export function listSimplePoints() {
  return request({
    url: '/rule/point/list-all-simple',
    method: 'get'
  })
}

// 查询检查点详细
export function getPoint(pointId) {
  return request({
    url: '/rule/point/' + pointId,
    method: 'get'
  })
}

// 查询检查点下拉树结构
export function treeselect() {
  return request({
    url: '/rule/point/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询检查点下拉树结构
export function rolePointTreeselect(roleId) {
  return request({
    url: '/rule/point/rolePointTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增检查点
export function addPoint(data) {
  return request({
    url: '/rule/point',
    method: 'post',
    data: data
  })
}

// 修改检查点
export function updatePoint(data) {
  return request({
    url: '/rule/point',
    method: 'put',
    data: data
  })
}

// 删除检查点
export function delPoint(pointId) {
  return request({
    url: '/rule/point/' + pointId,
    method: 'delete'
  })
}
