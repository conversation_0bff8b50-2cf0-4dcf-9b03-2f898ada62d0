import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/depts/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/depts/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/depts/' + deptId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/depts',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/depts',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: '/system/depts/' + deptId,
    method: 'delete'
  })
}

// 获取业务系统列表
export function resSystemList(params) {
  return request({
    url: '/res/system/list',
    method: 'get',
    params
  })
}
