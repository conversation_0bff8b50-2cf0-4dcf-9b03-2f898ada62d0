import request from '@/utils/request'

// 查询租户授权列表
export function listTenantAuth(query) {
  return request({
    url: '/system/authz/list',
    method: 'get',
    params: query
  })
}

// 查询租户授权详细
export function getTenantAuth(id) {
  return request({
    url: '/system/authz/' + id,
    method: 'get'
  })
}

// 新增租户授权
export function addTenantAuth(data) {
  return request({
    url: '/system/authz',
    method: 'post',
    data: data
  })
}

// 修改租户授权
export function updateTenantAuth(data) {
  return request({
    url: '/system/authz',
    method: 'put',
    data: data
  })
}

// 删除租户授权
export function delTenantAuth(id) {
  return request({
    url: '/system/authz/' + id,
    method: 'delete'
  })
}

// 导出租户授权
export function exportTenantAuth(query) {
  return request({
    url: '/system/authz/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/system/authz/importTemplate',
    method: 'get'
  })
}
