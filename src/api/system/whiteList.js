import request from '@/utils/request'

// 创建或修改白名单
export function updateWhiteList(data) {
  return request({
    url: '/risk/warn/addWhiteListRule',
    method: 'post',
    data: data
  })
}

// 获得白名单分页
export function getWhiteListPage(query) {
  return request({
    url: '/risk/warn/getWhiteListRule',
    method: 'get',
    params: query
  })
}

export function getWhiteListPageApi(query) {
  return request({
    url: '/risk/warn/getWhiteListRuleByApi',
    method: 'get',
    params: query
  })
}

// 获取告警类型
export function getWarnTypeName(query) {
  return request({
    url: '/risk/warn/getWarnTypeName',
    method: 'post',
    data: query
  })
}

// 获取匹配规则类型
export function getRulesList(query) {
  return request({
    url: '/risk/warn/getWhiteListRuleFieldsByWarnType',
    method: 'post',
    data: query
  })
}

// 查询白名单详情
export function getWhiteList(data) {
  return request({
    url: '/risk/warn/queryWhiteRuleDetail',
    method: 'post',
    data: data
  })
}

// 处置，标记，加入白名单
export function disposeContainer(data) {
  return request({
    url: '/risk/warn/disposeContainer',
    method: 'post',
    data: data
  })
}

/** 更新白名单规则 升级为通用规则 */
export function riskWarnUpdateContainerSafeType(params) {
  return request({
    url: `/risk/warn/updateContainerSafeType/${params.id}`,
    method: 'get'
  })
}
