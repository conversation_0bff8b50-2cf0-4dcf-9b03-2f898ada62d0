import request from '@/utils/request'

// 查询漏洞库版本列表
export function listWebTestingKnow(query) {
  return request({
    url: '/know/webTestingKnow/list',
    method: 'get',
    params: query
  })
}

// 查询漏洞库版本详细
export function getWebTestingKnow(id) {
  return request({
    url: '/know/webTestingKnow/' + id,
    method: 'get'
  })
}

// 新增漏洞库版本
export function addWebTestingKnow(data) {
  return request({
    url: '/know/webTestingKnow',
    method: 'post',
    data: data
  })
}

// 修改漏洞库版本
export function updateWebTestingKnow(data) {
  return request({
    url: '/know/webTestingKnow',
    method: 'put',
    data: data
  })
}

// 删除漏洞库版本
export function delWebTestingKnow(id) {
  return request({
    url: '/know/webTestingKnow/' + id,
    method: 'delete'
  })
}

// 导出漏洞库版本
export function exportWebTestingKnow(query) {
  return request({
    url: '/know/webTestingKnow/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/webTestingKnow/importTemplate',
    method: 'get'
  })
}
