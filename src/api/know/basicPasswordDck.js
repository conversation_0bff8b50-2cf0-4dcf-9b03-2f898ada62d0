import request from '@/utils/request'

// 查询弱口令字典列表
export function listBasicPasswordDck(query) {
  return request({
    url: '/know/basicPasswordDck/list',
    method: 'get',
    params: query
  })
}

// 查询弱口令字典详细
export function getBasicPasswordDck(id) {
  return request({
    url: '/know/basicPasswordDck/' + id,
    method: 'get'
  })
}

// 新增弱口令字典
export function addBasicPasswordDck(data) {
  return request({
    url: '/know/basicPasswordDck',
    method: 'post',
    data: data
  })
}

// 修改弱口令字典
export function updateBasicPasswordDck(data) {
  return request({
    url: '/know/basicPasswordDck',
    method: 'put',
    data: data
  })
}

// 删除弱口令字典
export function delBasicPasswordDck(id) {
  return request({
    url: '/know/basicPasswordDck/' + id,
    method: 'delete'
  })
}

// 导出弱口令字典
export function exportBasicPasswordDck(query) {
  return request({
    url: '/know/basicPasswordDck/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/basicPasswordDck/importTemplate',
    method: 'get'
  })
}
