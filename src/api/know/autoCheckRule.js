import request from '@/utils/request'

// 查询自动校验策略列表
export function listAutoCheckRule(query) {
  return request({
    url: '/know/autoCheckRule/list',
    method: 'get',
    params: query
  })
}

// 查询自动校验策略详细
export function getAutoCheckRule(id) {
  return request({
    url: '/know/autoCheckRule/getAutoCheckRuleMsg/' + id,
    method: 'get'
  })
}

// 新增自动校验策略
export function addAutoCheckRule(data) {
  return request({
    url: '/know/autoCheckRule/addAutoCheckRule',
    method: 'post',
    data: data
  })
}

// 修改自动校验策略
export function updateAutoCheckRule(data) {
  return request({
    url: '/know/autoCheckRule/addAutoCheckRule',
    method: 'post',
    data: data
  })
}

// 删除自动校验策略
export function delAutoCheckRule(id) {
  return request({
    url: '/know/autoCheckRule/' + id,
    method: 'delete'
  })
}

// 导出自动校验策略
export function exportAutoCheckRule(query) {
  return request({
    url: '/know/autoCheckRule/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/autoCheckRule/importTemplate',
    method: 'get'
  })
}
