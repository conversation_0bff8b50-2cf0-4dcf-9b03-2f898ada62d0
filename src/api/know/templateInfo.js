import request from '@/utils/request'

// 查询基线模板信息列表
export function listTemplateInfo(query) {
  return request({
    url: '/know/templateInfo/list',
    method: 'get',
    params: query
  })
}

// 查询基线模板信息详细
export function getTemplateInfo(id) {
  return request({
    url: '/know/templateInfo/' + id,
    method: 'get'
  })
}

// 新增基线模板信息
export function addTemplateInfo(data) {
  return request({
    url: '/know/templateInfo',
    method: 'post',
    data: data
  })
}

// 修改基线模板信息
export function updateTemplateInfo(data) {
  return request({
    url: '/know/templateInfo',
    method: 'put',
    data: data
  })
}

// 删除基线模板信息
export function delTemplateInfo(id) {
  return request({
    url: '/know/templateInfo/' + id,
    method: 'delete'
  })
}

// 导出基线模板信息
export function exportTemplateInfo(query) {
  return request({
    url: '/know/templateInfo/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/templateInfo/importTemplate',
    method: 'get'
  })
}
