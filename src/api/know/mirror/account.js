import request from '@/utils/request'

// 查询漏洞库版本列表
export function listPzAccountInfo(query) {
  return request({
    url: '/pz/account/info/list',
    method: 'get',
    params: query
  })
}

// 查询漏洞库版本详细
export function getPzAccountInfo(id) {
  return request({
    url: '/pz/account/info/' + id,
    method: 'get'
  })
}

// 新增漏洞库版本
export function addPzAccountInfo(data) {
  return request({
    url: '/pz/account/info',
    method: 'post',
    data: data
  })
}

// 修改漏洞库版本
export function updatePzAccountInfo(data) {
  return request({
    url: '/pz/account/info',
    method: 'put',
    data: data
  })
}

// 删除漏洞库版本
export function delPzAccountInfo(id) {
  return request({
    url: '/pz/account/info/' + id,
    method: 'delete'
  })
}

// 导出漏洞库版本
export function exportPzAccountInfo(query) {
  return request({
    url: '/pz/account/info/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/pz/account/info/importTemplate',
    method: 'get'
  })
}
