import request from '@/utils/request'

// 查询系统漏洞知识库列表
export function listBasicSystemLibrary(query) {
  return request({
    url: '/know/basicSystemLibrary/list',
    method: 'get',
    params: query
  })
}

// 查询系统漏洞知识库详细
export function getBasicSystemLibrary(id) {
  return request({
    url: '/know/basicSystemLibrary/' + id,
    method: 'get'
  })
}

// 新增系统漏洞知识库
export function addBasicSystemLibrary(data) {
  return request({
    url: '/know/basicSystemLibrary',
    method: 'post',
    data: data
  })
}

// 修改系统漏洞知识库
export function updateBasicSystemLibrary(data) {
  return request({
    url: '/know/basicSystemLibrary',
    method: 'put',
    data: data
  })
}

// 删除系统漏洞知识库
export function delBasicSystemLibrary(id) {
  return request({
    url: '/know/basicSystemLibrary/' + id,
    method: 'delete'
  })
}

// 导出系统漏洞知识库
export function exportBasicSystemLibrary(query) {
  return request({
    url: '/know/basicSystemLibrary/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/basicSystemLibrary/importTemplate',
    method: 'get'
  })
}
