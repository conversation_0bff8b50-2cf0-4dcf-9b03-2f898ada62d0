import request from '@/utils/request'

// 查询弱口令策略列表
export function listBasicPasswordRule(query) {
  return request({
    url: '/know/basicPasswordRule/list',
    method: 'get',
    params: query
  })
}

// 查询弱口令策略详细
export function getBasicPasswordRule(id) {
  return request({
    url: '/know/basicPasswordRule/' + id,
    method: 'get'
  })
}

// 新增弱口令策略
export function addBasicPasswordRule(data) {
  return request({
    url: '/know/basicPasswordRule',
    method: 'post',
    data: data
  })
}

// 修改弱口令策略
export function updateBasicPasswordRule(data) {
  return request({
    url: '/know/basicPasswordRule',
    method: 'put',
    data: data
  })
}

// 删除弱口令策略
export function delBasicPasswordRule(id) {
  return request({
    url: '/know/basicPasswordRule/' + id,
    method: 'delete'
  })
}

// 导出弱口令策略
export function exportBasicPasswordRule(query) {
  return request({
    url: '/know/basicPasswordRule/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/basicPasswordRule/importTemplate',
    method: 'get'
  })
}
