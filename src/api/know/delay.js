import request from '@/utils/request'

// 查询知识库自动延期管理列表
export function listKnowDelay(query) {
  return request({
    url: '/know/delay/list',
    method: 'get',
    params: query
  })
}

// 查询知识库自动延期管理详细
export function getKnowDelay(id) {
  return request({
    url: '/know/delay/' + id,
    method: 'get'
  })
}

// 新增知识库自动延期管理
export function addKnowDelay(data) {
  return request({
    url: '/know/delay',
    method: 'post',
    data: data
  })
}

// 修改知识库自动延期管理
export function updateKnowDelay(data) {
  return request({
    url: '/know/delay',
    method: 'put',
    data: data
  })
}

// 删除知识库自动延期管理
export function delKnowDelay(id) {
  return request({
    url: '/know/delay/' + id,
    method: 'delete'
  })
}

// 导出知识库自动延期管理
export function exportKnowDelay(query) {
  return request({
    url: '/know/delay/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/delay/importTemplate',
    method: 'get'
  })
}
