import request from '@/utils/request'

// 查询基础安全脚本管理列表
export function listScriptManage(query) {
  return request({
    url: '/know/scriptManage/list',
    method: 'get',
    params: query
  })
}

// 查询基础安全脚本管理详细
export function getScriptManage(id) {
  return request({
    url: '/know/scriptManage/' + id,
    method: 'get'
  })
}

// 新增基础安全脚本管理
export function addScriptManage(data) {
  return request({
    url: '/know/scriptManage',
    method: 'post',
    data: data
  })
}

// 修改基础安全脚本管理
export function updateScriptManage(data) {
  return request({
    url: '/know/scriptManage',
    method: 'put',
    data: data
  })
}

// 删除基础安全脚本管理
export function delScriptManage(id) {
  return request({
    url: '/know/scriptManage/' + id,
    method: 'delete'
  })
}

// 导出基础安全脚本管理
export function exportScriptManage(query) {
  return request({
    url: '/know/scriptManage/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/scriptManage/importTemplate',
    method: 'get'
  })
}
