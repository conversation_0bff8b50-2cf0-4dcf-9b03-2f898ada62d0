import request from '@/utils/request'

// 查询基线知识库列表
export function listBasicLineLibrary(query) {
  return request({
    url: '/know/basicLineLibrary/list',
    method: 'get',
    params: query
  })
}

// 查询基线知识库详细
export function getBasicLineLibrary(id) {
  return request({
    url: '/know/basicLineLibrary/' + id,
    method: 'get'
  })
}

// 新增基线知识库
export function addBasicLineLibrary(data) {
  return request({
    url: '/know/basicLineLibrary',
    method: 'post',
    data: data
  })
}

// 修改基线知识库
export function updateBasicLineLibrary(data) {
  return request({
    url: '/know/basicLineLibrary',
    method: 'put',
    data: data
  })
}

// 删除基线知识库
export function delBasicLineLibrary(id) {
  return request({
    url: '/know/basicLineLibrary/' + id,
    method: 'delete'
  })
}

// 导出基线知识库
export function exportBasicLineLibrary(query) {
  return request({
    url: '/know/basicLineLibrary/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/basicLineLibrary/importTemplate',
    method: 'get'
  })
}
