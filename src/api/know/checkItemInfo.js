import request from '@/utils/request'

// 查询基线检查项信息列表
export function listCheckItemInfo(query) {
  return request({
    url: '/know/checkItemInfo/list',
    method: 'get',
    params: query
  })
}

// 查询基线检查项信息详细
export function getCheckItemInfo(id) {
  return request({
    url: '/know/checkItemInfo/' + id,
    method: 'get'
  })
}

// 新增基线检查项信息
export function addCheckItemInfo(data) {
  return request({
    url: '/know/checkItemInfo',
    method: 'post',
    data: data
  })
}

// 修改基线检查项信息
export function updateCheckItemInfo(data) {
  return request({
    url: '/know/checkItemInfo',
    method: 'put',
    data: data
  })
}

// 删除基线检查项信息
export function delCheckItemInfo(id) {
  return request({
    url: '/know/checkItemInfo/' + id,
    method: 'delete'
  })
}

// 导出基线检查项信息
export function exportCheckItemInfo(query) {
  return request({
    url: '/know/checkItemInfo/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/know/checkItemInfo/importTemplate',
    method: 'get'
  })
}
