import request from '@/utils/request'

// 查询审核流程列表
export function listReviewFlow(query) {
  return request({
    url: '/sys/audit/list',
    method: 'get',
    params: query
  })
}

// 查询审核流程详细
export function getReviewFlow(id) {
  return request({
    url: '/sys/audit/' + id,
    method: 'get'
  })
}

// 新增审核流程
export function addReviewFlow(data) {
  return request({
    url: '/sys/audit',
    method: 'post',
    data: data
  })
}

// 修改审核流程
export function updateReviewFlow(data) {
  return request({
    url: '/sys/audit',
    method: 'put',
    data: data
  })
}

// 删除审核流程
export function delReviewFlow(id) {
  return request({
    url: '/sys/audit/' + id,
    method: 'delete'
  })
}

// 导出审核流程
export function exportReviewFlow(query) {
  return request({
    url: '/sys/audit/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/sys/audit/importTemplate',
    method: 'get'
  })
}
