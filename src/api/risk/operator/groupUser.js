import request from '@/utils/request'

// 删除minio文件
export function removeMinioFile(data) {
  return request({
    url: '/minIOController/removes',
    method: 'post',
    data: data
  })
}

// 查询异常操作人员分组列表
export function listGroupUser(query) {
  return request({
    url: '/risk/operator/groupUser/list',
    method: 'get',
    params: query
  })
}

// 查询异常操作人员分组详细
export function getGroupUser(id) {
  return request({
    url: '/risk/operator/groupUser/' + id,
    method: 'get'
  })
}

// 新增异常操作人员分组
export function addGroupUser(data) {
  return request({
    url: '/risk/operator/groupUser',
    method: 'post',
    data: data
  })
}

// 修改异常操作人员分组
export function updateGroupUser(data) {
  return request({
    url: '/risk/operator/groupUser',
    method: 'put',
    data: data
  })
}

// 删除异常操作人员分组
export function delGroupUser(id) {
  return request({
    url: '/risk/operator/groupUser/' + id,
    method: 'delete'
  })
}

// 导出异常操作人员分组
export function exportGroupUser(query) {
  return request({
    url: '/risk/operator/groupUser/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/operator/groupUser/importTemplate',
    method: 'get'
  })
}
