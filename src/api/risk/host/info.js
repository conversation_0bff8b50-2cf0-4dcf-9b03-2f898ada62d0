import request from '@/utils/request'

// 查询主机信息列表
export function listInfo(query) {
  return request({
    url: '/risk/host/info/list',
    method: 'get',
    params: query
  })
}

// 查询主机信息详细
export function getInfo(id) {
  return request({
    url: '/risk/host/info/' + id,
    method: 'get'
  })
}

// 新增主机信息
export function addInfo(data) {
  return request({
    url: '/risk/host/info',
    method: 'post',
    data: data
  })
}

// 修改主机信息
export function updateInfo(data) {
  return request({
    url: '/risk/host/info',
    method: 'put',
    data: data
  })
}

// 删除主机信息
export function delInfo(id) {
  return request({
    url: '/risk/host/info/' + id,
    method: 'delete'
  })
}

// 导出主机信息
export function exportInfo(query) {
  return request({
    url: '/risk/host/info/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/host/info/importTemplate',
    method: 'get'
  })
}
