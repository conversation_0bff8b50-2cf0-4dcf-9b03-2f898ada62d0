import request from '@/utils/request'

// 查询基础安全子任务列表
export function listReportSubtask(query) {
  return request({
    url: '/risk/reportSubtask/list',
    method: 'get',
    params: query
  })
}

// 查询基础安全子任务详细
export function getReportSubtask(id) {
  return request({
    url: '/risk/reportSubtask/' + id,
    method: 'get'
  })
}

// 新增基础安全子任务
export function addReportSubtask(data) {
  return request({
    url: '/risk/reportSubtask',
    method: 'post',
    data: data
  })
}

// 修改基础安全子任务
export function updateReportSubtask(data) {
  return request({
    url: '/risk/reportSubtask',
    method: 'put',
    data: data
  })
}

// 删除基础安全子任务
export function delReportSubtask(id) {
  return request({
    url: '/risk/reportSubtask/' + id,
    method: 'delete'
  })
}

// 导出基础安全子任务
export function exportReportSubtask(query) {
  return request({
    url: '/risk/reportSubtask/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/reportSubtask/importTemplate',
    method: 'get'
  })
}
