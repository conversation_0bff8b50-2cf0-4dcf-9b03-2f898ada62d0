import request from '@/utils/request'

// 查询异常分组策略列表
export function listGroupTransfer(query) {
  return request({
    url: '/risk/groupTransfer/list',
    method: 'get',
    params: query
  })
}

// 查询异常分组策略详细
export function getGroupTransfer(id) {
  return request({
    url: '/risk/groupTransfer/' + id,
    method: 'get'
  })
}

// 新增异常分组策略
export function addGroupTransfer(data) {
  return request({
    url: '/risk/groupTransfer',
    method: 'post',
    data: data
  })
}

// 修改异常分组策略
export function updateGroupTransfer(data) {
  return request({
    url: '/risk/groupTransfer',
    method: 'put',
    data: data
  })
}

// 删除异常分组策略
export function delGroupTransfer(id) {
  return request({
    url: '/risk/groupTransfer/' + id,
    method: 'delete'
  })
}

// 导出异常分组策略
export function exportGroupTransfer(query) {
  return request({
    url: '/risk/groupTransfer/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/groupTransfer/importTemplate',
    method: 'get'
  })
}
