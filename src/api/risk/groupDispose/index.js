import request from '@/utils/request'

// 查询异常处置分组列表
export function listRiskOperatorGroupName(query) {
  return request({
    url: '/common/riskOperatorGroupName/list',
    method: 'get',
    params: query
  })
}

// 查询异常处置分组详细
export function getRiskOperatorGroupName(id) {
  return request({
    url: '/common/riskOperatorGroupName/' + id,
    method: 'get'
  })
}

// 新增异常处置分组
export function addRiskOperatorGroupName(data) {
  return request({
    url: '/common/riskOperatorGroupName',
    method: 'post',
    data: data
  })
}

// 修改异常处置分组
export function updateRiskOperatorGroupName(data) {
  return request({
    url: '/common/riskOperatorGroupName',
    method: 'put',
    data: data
  })
}

// 删除异常处置分组
export function delRiskOperatorGroupName(id) {
  return request({
    url: '/common/riskOperatorGroupName/' + id,
    method: 'delete'
  })
}

// 导出异常处置分组
export function exportRiskOperatorGroupName(query) {
  return request({
    url: '/common/riskOperatorGroupName/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/common/riskOperatorGroupName/importTemplate',
    method: 'get'
  })
}
