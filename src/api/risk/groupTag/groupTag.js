import request from '@/utils/request'

// 查询基础安全分组标签列表
export function listGroupTag(query) {
  return request({
    url: '/risk/groupTag/list',
    method: 'get',
    params: query
  })
}

// 查询基础安全分组标签详细
export function getGroupTag(id) {
  return request({
    url: '/risk/groupTag/' + id,
    method: 'get'
  })
}

// 新增基础安全分组标签
export function addGroupTag(data) {
  return request({
    url: '/risk/groupTag',
    method: 'post',
    data: data
  })
}

// 修改基础安全分组标签
export function updateGroupTag(data) {
  return request({
    url: '/risk/groupTag',
    method: 'put',
    data: data
  })
}

// 删除基础安全分组标签
export function delGroupTag(id) {
  return request({
    url: '/risk/groupTag/' + id,
    method: 'delete'
  })
}

// 导出基础安全分组标签
export function exportGroupTag(query) {
  return request({
    url: '/risk/groupTag/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/groupTag/importTemplate',
    method: 'get'
  })
}
