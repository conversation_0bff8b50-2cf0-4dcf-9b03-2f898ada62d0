import request from '@/utils/request'

// 查询分组标签策略列表
export function listGroupTagRule(query) {
  return request({
    url: '/risk/groupTagRule/list',
    method: 'get',
    params: query
  })
}

// 查询分组标签策略详细
export function getGroupTagRule(id) {
  return request({
    url: '/risk/groupTagRule/' + id,
    method: 'get'
  })
}

// 新增分组标签策略
export function addGroupTagRule(data) {
  return request({
    url: '/risk/groupTagRule',
    method: 'post',
    data: data
  })
}

// 修改分组标签策略
export function updateGroupTagRule(data) {
  return request({
    url: '/risk/groupTagRule',
    method: 'put',
    data: data
  })
}

// 删除分组标签策略
export function delGroupTagRule(id) {
  return request({
    url: '/risk/groupTagRule/' + id,
    method: 'delete'
  })
}

// 导出分组标签策略
export function exportGroupTagRule(query) {
  return request({
    url: '/risk/groupTagRule/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/groupTagRule/importTemplate',
    method: 'get'
  })
}
