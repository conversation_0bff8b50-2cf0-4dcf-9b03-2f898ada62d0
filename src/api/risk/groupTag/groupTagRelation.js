import request from '@/utils/request'

// 查询组与标签关联关系列表
export function listGroupTagRelation(query) {
  return request({
    url: '/risk/groupTagRelation/list',
    method: 'get',
    params: query
  })
}

// 查询组与标签关联关系详细
export function getGroupTagRelation(id) {
  return request({
    url: '/risk/groupTagRelation/' + id,
    method: 'get'
  })
}

// 新增组与标签关联关系
export function addGroupTagRelation(data) {
  return request({
    url: '/risk/groupTagRelation',
    method: 'post',
    data: data
  })
}

// 修改组与标签关联关系
export function updateGroupTagRelation(data) {
  return request({
    url: '/risk/groupTagRelation',
    method: 'put',
    data: data
  })
}

// 删除组与标签关联关系
export function delGroupTagRelation(id) {
  return request({
    url: '/risk/groupTagRelation/' + id,
    method: 'delete'
  })
}

// 导出组与标签关联关系
export function exportGroupTagRelation(query) {
  return request({
    url: '/risk/groupTagRelation/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/groupTagRelation/importTemplate',
    method: 'get'
  })
}
