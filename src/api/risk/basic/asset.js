import request from '@/utils/request'

// 查询资产核查列表
export function listAsset(query) {
  return request({
    url: '/risk/host/list',
    method: 'get',
    params: query
  })
}

// 查询资产核查详细
export function getAsset(id) {
  return request({
    url: '/risk/host/' + id,
    method: 'get'
  })
}

// 新增资产核查
export function addAsset(data) {
  return request({
    url: '/risk/host',
    method: 'post',
    data
  })
}
