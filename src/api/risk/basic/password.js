import request from '@/utils/request'

// 查询弱口令漏洞列表
export function listPassword(query) {
  return request({
    url: '/risk/basic/password/list',
    method: 'get',
    params: query
  })
}

// 查询弱口令漏洞详细
export function getPassword(id) {
  return request({
    url: '/risk/basic/password/' + id,
    method: 'get'
  })
}

// 新增弱口令漏洞
export function addPassword(data) {
  return request({
    url: '/risk/basic/password',
    method: 'post',
    data: data
  })
}

// 修改弱口令漏洞
export function updatePassword(data) {
  return request({
    url: '/risk/basic/password',
    method: 'put',
    data: data
  })
}

// 删除弱口令漏洞
export function delPassword(id) {
  return request({
    url: '/risk/basic/password/' + id,
    method: 'delete'
  })
}

// 导出弱口令漏洞
export function exportPassword(query) {
  return request({
    url: '/risk/basic/password/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/basic/password/importTemplate',
    method: 'get'
  })
}
