import request from '@/utils/request'

// 查询基线漏洞列表
export function listLine(query) {
  return request({
    url: '/risk/basic/line/list',
    method: 'get',
    params: query
  })
}

// 查询基线漏洞详细
export function getLine(id) {
  return request({
    url: '/risk/basic/line/' + id,
    method: 'get'
  })
}

// 新增基线漏洞
export function addLine(data) {
  return request({
    url: '/risk/basic/line',
    method: 'post',
    data: data
  })
}

// 修改基线漏洞
export function updateLine(data) {
  return request({
    url: '/risk/basic/line',
    method: 'put',
    data: data
  })
}

// 删除基线漏洞
export function delLine(id) {
  return request({
    url: '/risk/basic/line/' + id,
    method: 'delete'
  })
}

// 导出基线漏洞
export function exportLine(query) {
  return request({
    url: '/risk/basic/line/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/basic/line/importTemplate',
    method: 'get'
  })
}
