import request from '@/utils/request'

// 查询系统漏洞列表
export function listStrategy(query) {
  return request({
    url: '/risk/strategy/list',
    method: 'get',
    params: query
  })
}

// 查询系统漏洞详细
export function getStrategy(id) {
  return request({
    url: '/risk/strategy/' + id,
    method: 'get'
  })
}

// 新增系统漏洞
export function addStrategy(data) {
  return request({
    url: '/risk/strategy',
    method: 'post',
    data: data
  })
}

// 修改系统漏洞
export function updateStrategy(data) {
  return request({
    url: '/risk/strategy',
    method: 'put',
    data: data
  })
}

// 删除系统漏洞
export function delStrategy(id) {
  return request({
    url: '/risk/strategy/' + id,
    method: 'delete'
  })
}

// 导出系统漏洞
export function exportStrategy(query) {
  return request({
    url: '/risk/strategy/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/strategy/importTemplate',
    method: 'get'
  })
}
