import request from '@/utils/request'

// 查询镜像漏洞列表
export function listMirror(query) {
  return request({
    url: '/risk/basicImage/list',
    method: 'get',
    params: query
  })
}

// 查询镜像漏洞详细
export function getMirror(id, params = {}) {
  return request({
    url: '/risk/basicImage/' + id,
    method: 'get',
    params
  })
}

// 新增镜像漏洞
export function addMirror(data) {
  return request({
    url: '/risk/basicImage',
    method: 'post',
    data
  })
}
