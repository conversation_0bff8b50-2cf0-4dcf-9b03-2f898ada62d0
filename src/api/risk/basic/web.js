import request from '@/utils/request'

// 查询应用漏洞列表
export function listWeb(query) {
  return request({
    url: '/risk/basic/web/list',
    method: 'get',
    params: query
  })
}

// 查询应用漏洞详细
export function getWeb(id) {
  return request({
    url: '/risk/basic/web/' + id,
    method: 'get'
  })
}

// 新增应用漏洞
export function addWeb(data) {
  return request({
    url: '/risk/basic/web',
    method: 'post',
    data: data
  })
}

// 修改应用漏洞
export function updateWeb(data) {
  return request({
    url: '/risk/basic/web',
    method: 'put',
    data: data
  })
}

// 删除应用漏洞
export function delWeb(id) {
  return request({
    url: '/risk/basic/web/' + id,
    method: 'delete'
  })
}

// 导出应用漏洞
export function exportWeb(query) {
  return request({
    url: '/risk/basic/web/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/basic/web/importTemplate',
    method: 'get'
  })
}

/** 改变渗透测试测试结果 */
export function riskBasicWebModifyWebTestingItemInfo(data) {
  return request({
    url: '/risk/basic/web/modifyWebTestingItemInfo',
    method: 'post',
    data
  })
}
