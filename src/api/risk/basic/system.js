import request from '@/utils/request'

// 查询系统漏洞列表
export function listSystem(query) {
  return request({
    url: '/risk/basic/system/list',
    method: 'get',
    params: query
  })
}

// 查询系统漏洞详细
export function getSystem(id) {
  return request({
    url: '/risk/basic/system/' + id,
    method: 'get'
  })
}

// 新增系统漏洞
export function addSystem(data) {
  return request({
    url: '/risk/basic/system',
    method: 'post',
    data: data
  })
}

// 修改系统漏洞
export function updateSystem(data) {
  return request({
    url: '/risk/basic/system',
    method: 'put',
    data: data
  })
}

// 删除系统漏洞
export function delSystem(id) {
  return request({
    url: '/risk/basic/system/' + id,
    method: 'delete'
  })
}

// 导出系统漏洞
export function exportSystem(query) {
  return request({
    url: '/risk/basic/system/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/basic/system/importTemplate',
    method: 'get'
  })
}

/**
 * 获取集群漏洞统计列表
 */
export function riskBasicSystemGetCountByClusterExport(data) {
  return request({
    method: 'get',
    url: '/risk/basic/system/getCountByClusterExport',
    params: data
  })
}
