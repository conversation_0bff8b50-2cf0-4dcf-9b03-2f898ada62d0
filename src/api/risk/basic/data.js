import request from '@/utils/request'
import { Loading } from 'element-ui'
import { saveAs } from 'file-saver'

export function minIOControllerDownload(data) {
  const downloadLoadingInstance = Loading.service({
    text: '正在下载数据，请稍候',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  const formData = new FormData()
  formData.append('fileName', data.fileName)
  formData.append('bucketName', data.bucketName)
  request({
    url: '/minIOController/downloadFile',
    method: 'post',
    data: formData,
    responseType: 'blob'
  }).then((response) => {
    const blob = new Blob([response])
    saveAs(blob, data.fileName)
  }).then((response) => {
    downloadLoadingInstance.close()
  })
}
