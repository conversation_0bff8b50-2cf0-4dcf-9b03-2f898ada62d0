import request from '@/utils/request'

export function riskReportTaskGetServiceSys(params = {}) {
  return request({
    url: '/risk/report/task/getServiceSys',
    method: 'get',
    params
  })
}

/** 系统|基线|弱口令|应用漏洞 */
export function riskWarnApproval(data = {}) {
  return request({
    url: '/risk/warn/approval',
    method: 'post',
    data
  })
}

/** 通用分配 */
export function riskDisposeAssign(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/assign',
    method: 'post',
    data
  })
}

/** 通用审核 */
export function riskDisposeAudit(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/audit',
    method: 'post',
    data
  })
}

/** 通用处置 */
export function riskDispose(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/dispose',
    method: 'post',
    data
  })
}

/** 通用退回 */
export function riskDisposeGoback(data = {}) {
  if (data.riskType) {
    data.riskBasicType = data.riskType
    delete data.riskType
  }

  return request({
    url: '/risk/dispose/goback',
    method: 'post',
    data
  })
}
