import request from '@/utils/request'

// 查询弱口令字典列表
export function listName(query) {
  return request({
    url: '/risk/name/list',
    method: 'get',
    params: query
  })
}

// 查询弱口令字典详细
export function getName(id) {
  return request({
    url: '/risk/name/' + id,
    method: 'get'
  })
}

// 新增弱口令字典
export function addName(data) {
  return request({
    url: '/risk/name',
    method: 'post',
    data: data
  })
}

// 修改弱口令字典
export function updateName(data) {
  return request({
    url: '/risk/name',
    method: 'put',
    data: data
  })
}

// 删除弱口令字典
export function delName(id) {
  return request({
    url: '/risk/name/' + id,
    method: 'delete'
  })
}

// 导出弱口令字典
export function exportName(query) {
  return request({
    url: '/risk/name/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/name/importTemplate',
    method: 'get'
  })
}
