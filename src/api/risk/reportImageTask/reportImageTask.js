import request from '@/utils/request'

// 查询镜像工单漏扫任务列表
export function listReportImageTask(query) {
  return request({
    url: '/risk/reportImageTask/list',
    method: 'get',
    params: query
  })
}

// 查询镜像工单漏扫任务详细
export function getReportImageTask(id) {
  return request({
    url: '/risk/reportImageTask/' + id,
    method: 'get'
  })
}

// 新增镜像工单漏扫任务
export function addReportImageTask(data) {
  return request({
    url: '/risk/reportImageTask',
    method: 'post',
    data: data
  })
}

// 修改镜像工单漏扫任务
export function updateReportImageTask(data) {
  return request({
    url: '/risk/reportImageTask',
    method: 'put',
    data: data
  })
}

// 删除镜像工单漏扫任务
export function delReportImageTask(id) {
  return request({
    url: '/risk/reportImageTask/' + id,
    method: 'delete'
  })
}

// 导出镜像工单漏扫任务
export function exportReportImageTask(query) {
  return request({
    url: '/risk/reportImageTask/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/reportImageTask/importTemplate',
    method: 'get'
  })
}
