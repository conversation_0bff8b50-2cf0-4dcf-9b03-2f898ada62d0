import request from '@/utils/request'

// 查询标签库列表
export function listLibrary(query) {
  return request({
    url: '/risk/tag/library/list',
    method: 'get',
    params: query
  })
}

// 查询标签库详细
export function getLibrary(id) {
  return request({
    url: '/risk/tag/library/' + id,
    method: 'get'
  })
}

// 新增标签库
export function addLibrary(data) {
  return request({
    url: '/risk/tag/library',
    method: 'post',
    data: data
  })
}

// 修改标签库
export function updateLibrary(data) {
  return request({
    url: '/risk/tag/library',
    method: 'put',
    data: data
  })
}

// 删除标签库
export function delLibrary(id) {
  return request({
    url: '/risk/tag/library/' + id,
    method: 'delete'
  })
}

// 导出标签库
export function exportLibrary(query) {
  return request({
    url: '/risk/tag/library/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/tag/library/importTemplate',
    method: 'get'
  })
}
