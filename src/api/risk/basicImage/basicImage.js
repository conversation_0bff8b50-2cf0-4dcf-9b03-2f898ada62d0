import request from '@/utils/request'

// 查询镜像工单漏洞信息列表
export function listBasicImage(query) {
  return request({
    url: '/risk/basicImage/list',
    method: 'get',
    params: query
  })
}

// 查询镜像工单漏洞信息详细
export function getBasicImage(id) {
  return request({
    url: '/risk/basicImage/' + id,
    method: 'get'
  })
}

// 新增镜像工单漏洞信息
export function addBasicImage(data) {
  return request({
    url: '/risk/basicImage',
    method: 'post',
    data: data
  })
}

// 修改镜像工单漏洞信息
export function updateBasicImage(data) {
  return request({
    url: '/risk/basicImage',
    method: 'put',
    data: data
  })
}

// 删除镜像工单漏洞信息
export function delBasicImage(id) {
  return request({
    url: '/risk/basicImage/' + id,
    method: 'delete'
  })
}

// 导出镜像工单漏洞信息
export function exportBasicImage(query) {
  return request({
    url: '/risk/basicImage/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/basicImage/importTemplate',
    method: 'get'
  })
}
