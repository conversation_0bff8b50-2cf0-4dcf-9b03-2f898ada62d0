import request from '@/utils/request'

// 查询API安全网关列表
export function listApi(query) {
  return request({
    url: '/risk/api/gateway/group/list',
    method: 'get',
    params: query
  })
}

// 查询API安全网关详细
export function getApi(id) {
  return request({
    url: '/risk/api/' + id,
    method: 'get'
  })
}

// 新增API安全网关
export function addApi(data) {
  return request({
    url: '/risk/api',
    method: 'post',
    data: data
  })
}

// 修改API安全网关
export function updateApi(data) {
  return request({
    url: '/risk/api',
    method: 'put',
    data: data
  })
}

// 删除API安全网关
export function delApi(id) {
  return request({
    url: '/risk/api/' + id,
    method: 'delete'
  })
}

// 导出API安全网关
export function exportApi(query) {
  return request({
    url: '/risk/api/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/api/importTemplate',
    method: 'get'
  })
}

// 查询API安全网关关联列表
export function relevanceListApi(query) {
  return request({
    url: '/risk/api/gateway/relevance/list',
    method: 'get',
    params: query
  })
}
