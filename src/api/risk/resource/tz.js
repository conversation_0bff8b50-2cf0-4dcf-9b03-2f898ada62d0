import request from '@/utils/request'

// 查询容器安全与微隔离-探真列表
export function listTz(query) {
  return request({
    url: '/risk/tz/group/list',
    method: 'get',
    params: query
  })
}

// 查询容器安全与微隔离-探真详细
export function getTz(id) {
  return request({
    url: '/risk/tz/' + id,
    method: 'get'
  })
}

// 新增容器安全与微隔离-探真
export function addTz(data) {
  return request({
    url: '/risk/tz',
    method: 'post',
    data: data
  })
}

// 修改容器安全与微隔离-探真
export function updateTz(data) {
  return request({
    url: '/risk/tz',
    method: 'put',
    data: data
  })
}

// 删除容器安全与微隔离-探真
export function delTz(id) {
  return request({
    url: '/risk/tz/' + id,
    method: 'delete'
  })
}

// 导出容器安全与微隔离-探真
export function exportTz(query) {
  return request({
    url: '/risk/tz/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/tz/importTemplate',
    method: 'get'
  })
}

// 查询容器安全与微隔离-探真关联聚合列表
export function relevanceListTz(query) {
  return request({
    url: '/risk/tz/relevance/list',
    method: 'get',
    params: query
  })
}
