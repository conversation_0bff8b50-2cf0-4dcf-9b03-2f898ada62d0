import request from '@/utils/request'

// 查询源代码审计详情列表
export function listSourceCodeAudit(query) {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail/list',
    method: 'get',
    params: query
  })
}

// 查询源代码审计详情信息
export function getSourceCodeAudit(taskId) {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail/' + taskId,
    method: 'get'
  })
}

// 新增源代码审计详情
export function addSourceCodeAudit(data) {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail',
    method: 'post',
    data: data
  })
}

// 修改源代码审计详情
export function updateSourceCodeAudit(data) {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail',
    method: 'put',
    data: data
  })
}

// 删除源代码审计详情
export function delSourceCodeAudit(taskId) {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail/' + taskId,
    method: 'delete'
  })
}

// 导出源代码审计详情
export function exportSourceCodeAudit(query) {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/sourceCodeAuditProblemDetail/importTemplate',
    method: 'get'
  })
}
