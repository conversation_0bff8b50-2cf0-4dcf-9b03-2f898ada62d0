import request from '@/utils/request'

// 查询 APP安全检测 列表
export function listAppTest(query) {
  return request({
    url: '/risk/app/list',
    method: 'get',
    params: query
  })
}

// 查询 APP安全检测 详细
export function getAppTest(taskId) {
  return request({
    url: '/risk/app/' + taskId,
    method: 'get'
  })
}

// 新增 APP安全检测
export function addAppTest(data) {
  return request({
    url: '/risk/app',
    method: 'post',
    data: data
  })
}

// 修改 APP安全检测
export function updateAppTest(data) {
  return request({
    url: '/risk/app',
    method: 'put',
    data: data
  })
}

// 删除 APP安全检测
export function delAppTest(taskId) {
  return request({
    url: '/risk/app/' + taskId,
    method: 'delete'
  })
}

// 导出 APP安全检测
export function exportAppTest(query) {
  return request({
    url: '/risk/app/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/app/importTemplate',
    method: 'get'
  })
}
