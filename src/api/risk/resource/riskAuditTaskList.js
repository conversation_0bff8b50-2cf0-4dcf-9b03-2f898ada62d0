import request from '@/utils/request'

// 查询源代码审计任务列列表
export function listRiskAuditTaskList(query) {
  return request({
    url: '/risk/riskAuditTaskList/list',
    method: 'get',
    params: query
  })
}

// 查询源代码审计任务列详细
export function getRiskAuditTaskList(taskId) {
  return request({
    url: '/risk/riskAuditTaskList/' + taskId,
    method: 'get'
  })
}

// 新增源代码审计任务列
export function addRiskAuditTaskList(data) {
  return request({
    url: '/risk/riskAuditTaskList',
    method: 'post',
    data: data
  })
}

// 修改源代码审计任务列
export function updateRiskAuditTaskList(data) {
  return request({
    url: '/risk/riskAuditTaskList',
    method: 'put',
    data: data
  })
}

// 删除源代码审计任务列
export function delRiskAuditTaskList(taskId) {
  return request({
    url: '/risk/riskAuditTaskList/' + taskId,
    method: 'delete'
  })
}

// 导出源代码审计任务列
export function exportRiskAuditTaskList(query) {
  return request({
    url: '/risk/riskAuditTaskList/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/riskAuditTaskList/importTemplate',
    method: 'get'
  })
}
