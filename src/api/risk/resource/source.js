import request from '@/utils/request'

// 查询源代码审计列表
export function listSourceCodeAudit(query) {
  return request({
    url: '/risk/sourceCodeAuditTask/list',
    method: 'get',
    params: query
  })
}

// 查询源代码审计详细
export function getSourceCodeAudit(taskId) {
  return request({
    url: '/risk/sourceCodeAuditTask/' + taskId,
    method: 'get'
  })
}

// 新增源代码审计
export function addSourceCodeAudit(data) {
  return request({
    url: '/risk/sourceCodeAuditTask',
    method: 'post',
    data: data
  })
}

// 修改源代码审计
export function updateSourceCodeAudit(data) {
  return request({
    url: '/risk/sourceCodeAuditTask',
    method: 'put',
    data: data
  })
}

// 删除源代码审计
export function delSourceCodeAudit(taskId) {
  return request({
    url: '/risk/sourceCodeAuditTask/' + taskId,
    method: 'delete'
  })
}

// 导出源代码审计
export function exportSourceCodeAudit(query) {
  return request({
    url: '/risk/sourceCodeAuditTask/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/sourceCodeAuditTask/importTemplate',
    method: 'get'
  })
}
