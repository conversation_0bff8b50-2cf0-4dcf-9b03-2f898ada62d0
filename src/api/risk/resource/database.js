import request from '@/utils/request'

// 查询数据库审计日志列表
export function listDatabase(query) {
  return request({
    url: '/risk/database/group/list',
    method: 'get',
    params: query
  })
}

// 查询数据库审计日志详细
export function getDatabase(id) {
  return request({
    url: '/risk/database/' + id,
    method: 'get'
  })
}

// 新增数据库审计日志
export function addDatabase(data) {
  return request({
    url: '/risk/database',
    method: 'post',
    data: data
  })
}

// 修改数据库审计日志
export function updateDatabase(data) {
  return request({
    url: '/risk/database',
    method: 'put',
    data: data
  })
}

// 删除数据库审计日志
export function delDatabase(id) {
  return request({
    url: '/risk/database/' + id,
    method: 'delete'
  })
}

// 导出数据库审计日志
export function exportDatabase(query) {
  return request({
    url: '/risk/database/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/database/importTemplate',
    method: 'get'
  })
}

// 查询数据库审计关联列表
export function relevanceListDatabase(query) {
  return request({
    url: 'risk/database/relevance/list',
    method: 'get',
    params: query
  })
}
