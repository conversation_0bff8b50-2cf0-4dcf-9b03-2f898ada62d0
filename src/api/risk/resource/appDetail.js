import request from '@/utils/request'

// 查询 APP安全检测明细 列表
export function listAppTestDetail(query) {
  return request({
    url: '/risk/app/detail/list',
    method: 'get',
    params: query
  })
}

// 查询 APP安全检测明细 详细
export function getAppTestDetail(taskId) {
  return request({
    url: '/risk/app/detail/' + taskId,
    method: 'get'
  })
}

// 新增 APP安全检测明细
export function addAppTestDetail(data) {
  return request({
    url: '/risk/app/detail',
    method: 'post',
    data: data
  })
}

// 修改 APP安全检测明细
export function updateAppTestDetail(data) {
  return request({
    url: '/risk/app/detail',
    method: 'put',
    data: data
  })
}

// 删除 APP安全检测明细
export function delAppTestDetail(taskId) {
  return request({
    url: '/risk/app/detail/' + taskId,
    method: 'delete'
  })
}

// 导出 APP安全检测明细
export function exportAppTestDetail(query) {
  return request({
    url: '/risk/app/detail/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/app/detail/importTemplate',
    method: 'get'
  })
}
