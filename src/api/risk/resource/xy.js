import request from '@/utils/request'

// 查询容器安全与微隔离-小佑列表
export function listXy(query) {
  return request({
    url: '/risk/xy/group/list',
    method: 'get',
    params: query
  })
}

// 查询容器安全与微隔离-小佑详细
export function getXy(id) {
  return request({
    url: '/risk/xy/' + id,
    method: 'get'
  })
}

// 新增容器安全与微隔离-小佑
export function addXy(data) {
  return request({
    url: '/risk/xy',
    method: 'post',
    data: data
  })
}

// 修改容器安全与微隔离-小佑
export function updateXy(data) {
  return request({
    url: '/risk/xy',
    method: 'put',
    data: data
  })
}

// 删除容器安全与微隔离-小佑
export function delXy(id) {
  return request({
    url: '/risk/xy/' + id,
    method: 'delete'
  })
}

// 导出容器安全与微隔离-小佑
export function exportXy(query) {
  return request({
    url: '/risk/xy/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/xy/importTemplate',
    method: 'get'
  })
}

// 查询容器安全与微隔离-小佑关联列表
export function relevanceListXy(query) {
  return request({
    url: '/risk/xy/relevance/list',
    method: 'get',
    params: query
  })
}
