import request from '@/utils/request'

// 查询周报模板信息列表
export function listInfo(query) {
  return request({
    url: '/risk/templateInfo/list',
    method: 'get',
    params: query
  })
}

// 查询周报模板信息详细
export function getInfo(id) {
  return request({
    url: '/risk/templateInfo/' + id,
    method: 'get'
  })
}

// 新增周报模板信息
export function addInfo(data) {
  return request({
    url: '/risk/templateInfo',
    method: 'post',
    data: data
  })
}

// 修改周报模板信息
export function updateInfo(data) {
  return request({
    url: '/risk/templateInfo',
    method: 'put',
    data: data
  })
}

// 删除周报模板信息
export function delInfo(id) {
  return request({
    url: '/risk/templateInfo/' + id,
    method: 'delete'
  })
}

// 导出周报模板信息
export function exportInfo(query) {
  return request({
    url: '/risk/templateInfo/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/templateInfo/importTemplate',
    method: 'get'
  })
}
