import request from '@/utils/request'

// 查询周报信息列表
export function listInfo(query) {
  return request({
    url: '/risk/monthly/list',
    method: 'get',
    params: query
  })
}

// 查询周报信息详细
export function getInfo(id) {
  return request({
    url: '/risk/monthly/' + id,
    method: 'get'
  })
}

// 新增周报信息
export function addInfo(data) {
  return request({
    url: '/risk/monthly',
    method: 'post',
    data: data
  })
}

// 修改周报信息
export function updateInfo(data) {
  return request({
    url: '/risk/monthly',
    method: 'put',
    data: data
  })
}

// 删除周报信息
export function delInfo(id) {
  return request({
    url: '/risk/monthly/' + id,
    method: 'delete'
  })
}

// 导出周报信息
export function exportInfo(query) {
  return request({
    url: '/risk/monthly/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/monthly/importTemplate',
    method: 'get'
  })
}
