import request from '@/utils/request'

// 查询列表
export function listInfo(query) {
  return request({
    url: '/risk/templateOutline/getTreeList',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getInfo(id) {
  return request({
    url: '/risk/templateOutline/' + id,
    method: 'get'
  })
}

// 新增
export function addInfo(data) {
  return request({
    url: '/risk/templateOutline',
    method: 'post',
    data: data
  })
}

// 修改
export function updateInfo(data) {
  return request({
    url: '/risk/templateOutline',
    method: 'put',
    data: data
  })
}

// 删除
export function delInfo(id) {
  return request({
    url: '/risk/templateOutline/' + id,
    method: 'delete'
  })
}

// 导出
export function exportInfo(query) {
  return request({
    url: '/risk/templateOutline/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/templateOutline/importTemplate',
    method: 'get'
  })
}
