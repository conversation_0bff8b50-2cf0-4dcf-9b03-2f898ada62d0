import request from '@/utils/request'

// 查询漏扫报告任务管理列表
export function listTask(query) {
  return request({
    url: '/risk/report/task/list',
    method: 'get',
    params: query,
    silent: true,
    ws: true
  })
}
// 监察任务列表
export function superviseTaskList(query) {
  return request({
    url: '/risk/report/task/bureauProjectManagerSuperviseTaskList',
    method: 'get',
    params: query,
    silent: true
  })
}

// 查询漏扫报告任务管理详细
export function getTask(id) {
  return request({
    url: '/risk/report/task/' + id,
    method: 'get'
  })
}

// 查询获取资源主机的全部数量
export function getTotalAssets() {
  return request({
    url: '/risk/report/task/getTotalAssets',
    method: 'get'
  })
}

// 新增漏扫报告任务管理
export function addTask(data) {
  return request({
    url: '/risk/report/task',
    method: 'post',
    data: data
  })
}

// 修改漏扫报告任务管理
export function updateTask(data) {
  return request({
    url: '/risk/report/task',
    method: 'put',
    data: data
  })
}

export function delTask(id) {
  return request({
    url: '/risk/report/task/' + id,
    method: 'delete'
  })
}

/** 获取 局方审批人/执行人 列表 */
export function riskReportTaskGetUserListByRoleKey(data) {
  return request({
    url: '/risk/report/task/getUserListByRoleKey',
    method: 'get',
    params: data
  })
}

/** 获取所属部门列表 */
export function resHostQueryDeptName(data) {
  return request({
    url: '/res/host/queryDeptName',
    method: 'get',
    params: data
  })
}

/** 获取所属系统列表 */
export function resHostQuerySystemName(data) {
  return request({
    url: '/res/host/querySystemName',
    method: 'get',
    params: data
  })
}

/** 任务审批/处理 */
export function riskReportTaskBureauApproval(data) {
  return request({
    url: '/risk/report/task/bureauApproval',
    method: 'post',
    data
  })
}

export function riskReportTaskApprovalRecordList(data) {
  return request({
    url: '/risk/report/task/ApprovalRecord/list',
    method: 'get',
    params: data
  })
}

/** 上云五同步详情列表 */
export function riskFiveSyncList(data) {
  return request({
    url: '/risk/fiveSync/list',
    method: 'get',
    params: data
  })
}

/** 上云五同步详情列表 */
export function getWorkOrderTaskByTaskId(data) {
  return request({
    url: '/risk/fiveSync/getWorkOrderTaskByTaskId',
    method: 'get',
    params: data
  })
}

/** 上云五同步确认通过复审 */
export function riskReportTaskUpperCloudFiveSyncReexamine(data = {}) {
  return request({
    url: '/risk/report/task/upperCloudFiveSyncReexamine',
    method: 'post',
    data
  })
}

/** 上云五同步详情列表导出 */
export function riskFiveSyncExport(data = {}) {
  return request({
    url: '/risk/fiveSync/export',
    method: 'post',
    data
  })
}
