import request from '@/utils/request'

// 查询系统漏洞主机信息列表
export function listInfo(query) {
  return request({
    url: '/risk/system/host/info/list',
    method: 'get',
    params: query
  })
}

// 查询系统漏洞主机信息详细
export function getInfo(id) {
  return request({
    url: '/risk/system/host/info/' + id,
    method: 'get'
  })
}

// 新增系统漏洞主机信息
export function addInfo(data) {
  return request({
    url: '/risk/system/host/info',
    method: 'post',
    data: data
  })
}

// 修改系统漏洞主机信息
export function updateInfo(data) {
  return request({
    url: '/risk/system/host/info',
    method: 'put',
    data: data
  })
}

// 删除系统漏洞主机信息
export function delInfo(id) {
  return request({
    url: '/risk/system/host/info/' + id,
    method: 'delete'
  })
}

// 导出系统漏洞主机信息
export function exportInfo(query) {
  return request({
    url: '/risk/system/host/info/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/system/host/info/importTemplate',
    method: 'get'
  })
}
