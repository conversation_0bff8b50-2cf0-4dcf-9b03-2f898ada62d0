import request from '@/utils/request'

// 查询异常和人员分组和系统用户关联列表
export function listGroupuserIndex(query) {
  return request({
    url: '/risk/group/userIndex/list',
    method: 'get',
    params: query
  })
}
export function listGroupUserList(query) {
  return request({
    url: '/risk/group/userIndex/getUserList',
    method: 'get',
    params: query
  })
}
// 查询未分配用户列表
export function getUnselectedList(query) {
  return request({
    url: '/risk/group/userIndex/getUnselectedList',
    method: 'get',
    params: query
  })
}

// 查询异常和人员分组和系统用户关联详细
export function getGroupuserIndex(sysUserId) {
  return request({
    url: '/risk/group/userIndex/' + sysUserId,
    method: 'get'
  })
}

// 新增异常和人员分组和系统用户关联
export function addGroupuserIndex(data) {
  return request({
    url: '/risk/group/userIndex',
    method: 'post',
    data: data
  })
}

// 批量选择分组人员
export function selectAllInsert(data) {
  return request({
    url: '/risk/group/userIndex/selectAllInsert',
    method: 'put',
    params: data
  })
}

// 批量取消用户授权
export function cancelAll(data) {
  return request({
    url: '/risk/group/userIndex/cancelAll',
    method: 'put',
    params: data
  })
}

// 单个取消用户授权
export function cancel(data) {
  return request({
    url: '/risk/group/userIndex/cancel',
    method: 'post',
    params: data
  })
}

// 修改异常和人员分组和系统用户关联
export function updateGroupuserIndex(data) {
  return request({
    url: '/risk/group/userIndex',
    method: 'put',
    data: data
  })
}

// 删除异常和人员分组和系统用户关联
export function delGroupuserIndex(sysUserId) {
  return request({
    url: '/risk/group/userIndex/' + sysUserId,
    method: 'delete'
  })
}

// 导出异常和人员分组和系统用户关联
export function exportGroupuserIndex(query) {
  return request({
    url: '/risk/group/userIndex/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/group/userIndex/importTemplate',
    method: 'get'
  })
}
