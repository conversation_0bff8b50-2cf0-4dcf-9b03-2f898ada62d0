import request from '@/utils/request'

// 查询异常分组策略列表
export function listGroupStrategy(query) {
  return request({
    url: '/risk/groupStrategy/list',
    method: 'get',
    params: query
  })
}

// 查询异常分组策略详细
export function getGroupStrategy(id) {
  return request({
    url: '/risk/groupStrategy/' + id,
    method: 'get'
  })
}

// 新增异常分组策略
export function addGroupStrategy(data) {
  return request({
    url: '/risk/groupStrategy',
    method: 'post',
    data: data
  })
}

// 修改异常分组策略
export function updateGroupStrategy(data) {
  return request({
    url: '/risk/groupStrategy',
    method: 'put',
    data: data
  })
}

// 删除异常分组策略
export function delGroupStrategy(id) {
  return request({
    url: '/risk/groupStrategy/' + id,
    method: 'delete'
  })
}

// 导出异常分组策略
export function exportGroupStrategy(query) {
  return request({
    url: '/risk/groupStrategy/export',
    method: 'get',
    params: query
  })
}

// 下载导入模板
export function importTemplate() {
  return request({
    url: '/risk/groupStrategy/importTemplate',
    method: 'get'
  })
}
