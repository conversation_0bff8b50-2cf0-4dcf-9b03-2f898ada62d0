<template>
  <el-dialog title="分配人员" :visible.sync="dialogMixin.visible" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--flat">
        <template #toolbar:before="{selected, selection}">
          <el-button type="primary" icon="el-icon-plus" @click="onUserAddClick">添加用户</el-button>
          <el-button type="warning" :disabled="!selected" icon="el-icon-circle-close" @click="onUserUnAssign(selection)">取消分配</el-button>
        </template>
        <template #table:after="{ }">
          <el-table-column v-slot="{ row }" label="操作" width="100" align="center" fixed="right">
            <el-button type="text" size="mini" @click="onUserUnAssign([row])">取消分配</el-button>
          </el-table-column>
        </template>
      </EleSheet>
    </div>

    <UserAddDialog ref="userAddDialogRef" />

    <!-- <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template> -->
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'

import UserAddDialog from './UserAddDialog/index.vue'

import request from '@/utils/request.js'

export default {
  dicts: ['sys_user_normal_disable'],
  components: {
    UserAddDialog
  },
  mixins: [dialogMixin()],
  data() {
    return {}
  },
  computed: {
    params() {
      return {
        ...this.dialogMixin.params
      }
    },
    sheetProps() {
      return {
        title: '分配人员',
        lazy: false,
        api: {
          list: (params) => request({
            url: '/risk/templateOutline/getUserList',
            method: 'get',
            params: {
              ...this.params,
              ...params
            }
          })
        },
        tableProps: {
          selection: true
        },
        idKey: 'userId',
        model: {
          userName: {
            label: '从账号',
            table: {
              align: 'left'
            }
          },
          nickName: {
            label: '名称',
            hidden: ['search']
          },
          email: {
            label: '邮箱',
            hidden: ['search']
          },
          phoneNumber: {
            label: '手机',
            hidden: ['search']
          },
          deptName: {
            label: '部门',
            hidden: ['search']
          },
          status: {
            label: '状态',
            type: 'select',
            hidden: ['search'],
            options: this.dict.type.sys_user_normal_disable
          }
        }
      }
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },
    submit() {},

    onUserAddClick() {
      this.$refs.userAddDialogRef.open({
        params: this.params,
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },

    async onUserUnAssign(rows) {
      const userIds = rows.map(item => item.userId).join(',')

      const params = {
        ...this.params,
        jobUserIds: userIds,
        operateStatus: '2'
      }

      try {
        await this.$confirm('是否取消分配选中的用户？', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return false
      }

      const res = await request({ url: '/risk/templateOutline/editUserIds', method: 'put', data: params })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    }
  }
}
</script>

<style></style>
