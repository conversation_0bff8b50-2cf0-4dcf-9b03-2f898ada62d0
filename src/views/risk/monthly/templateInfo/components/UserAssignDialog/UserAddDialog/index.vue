<template>
  <el-dialog title="添加用户" :visible.sync="dialogMixin.visible" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--flat" @selection-change="onSelectionChange">
      </EleSheet>
    </div>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" :disabled="!selection.length" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'

import request from '@/utils/request.js'

export default {
  dicts: ['sys_user_normal_disable'],
  mixins: [dialogMixin()],
  data() {
    return {
      selection: []
    }
  },
  computed: {
    params() {
      return {
        ...this.dialogMixin.params
      }
    },
    sheetProps() {
      return {
        title: '分配人员',
        lazy: false,
        api: {
          list: (params) => request({
            url: '/risk/templateOutline/getUser',
            method: 'get',
            params: {
              ...this.params,
              ...params
            }
          })
        },

        tableProps: {
          selection: true
        },

        idKey: 'userId',

        model: {
          userName: {
            label: '从账号',
            table: {
              align: 'left'
            }
          },
          nickName: {
            label: '名称',
            hidden: ['search']
          },
          email: {
            label: '邮箱',
            hidden: ['search']
          },
          phoneNumber: {
            label: '手机'
          },
          deptName: {
            label: '部门',
            hidden: ['search']
          },
          status: {
            label: '状态',
            type: 'select',
            hidden: ['search'],
            options: this.dict.type.sys_user_normal_disable
          }
        }
      }
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },
    async submit() {
      const userIds = this.selection.map(item => item.userId).join(',')

      const params = {
        ...this.params,
        jobUserIds: userIds,
        operateStatus: '1'
      }

      const res = await request({ url: '/risk/templateOutline/editUserIds', method: 'put', data: params })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.dialogMixin.success()
        this.close()
      }
    },
    onSelectionChange(selection) {
      this.selection = selection
    }

  }
}
</script>

<style></style>
