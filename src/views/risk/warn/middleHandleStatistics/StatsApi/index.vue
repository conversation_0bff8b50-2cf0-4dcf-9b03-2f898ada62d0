<template>
  <div class="flex items-center space-x-4 overflow-auto">
    <div
      v-for="(item, index) of statsModel"
      :key="index"
      class="overview-statistics-item sa-flex flex-1 w-0 min-w-42"
    >
      <img :src="item.img" />
      <div>
        <div class="num">{{ item.num }}</div>
        <div class="title !text-xs">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { dataMixin } from '@/mixins'
import request from '@/utils/request.js'

export default {
  mixins: [
    dataMixin({
      statsInfo: {
        lazy: true,
        default: {
          'riskNotHandleNum': 0,
          'riskHandleCompleteNum': 0,
          'riskNotYetHandleNum': 0,
          'riskWhiteListNum': 0,
          'riskIgnoreNum': 0,
          'riskAlreadyHandle': 0
        },
        async load() {
          const res = await request({
            url: '/risk/warn/middleHandleStatistics',
            method: 'get',
            params: {
              ...this.params
            }
          })

          const data = res.data

          return data
        }
      }
    })
  ],
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    lazy: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      iconList: [
        require('@/assets/images/warn-handle-complete.png'),
        require('@/assets/images/warn-not-handle.png'),
        require('@/assets/images/warn-not-yet-handle.png')
      ]
    }
  },
  computed: {
    statsModel() {
      const value = [
        {
          img: this.iconList[1],
          num: this.dataMixin.statsInfo.riskHandleCompleteNum || 0,
          title: '已处置'
        },
        {
          img: this.iconList[0],
          num: this.dataMixin.statsInfo.riskNotHandleNum || 0,
          title: '未处置'
        },
        {
          img: this.iconList[2],
          num: this.dataMixin.statsInfo.riskIgnoreNum || 0,
          title: '已忽略'
        }
      ]

      return value
    }
  },
  created() {
    if (!this.lazy) {
      this.load()
    }
  },
  methods: {
    load() {
      this.dataMixin.load()
    },
    getMiddleHandleStatistics() {
      this.load()
    },
    getRiskAuditCount() {
      this.load()
    }
  }
}
</script>

<style lang="postcss" scoped>
  .overview-statistics-item {
    height: 80px;
    border-radius: 8px;
    background: linear-gradient(
        180deg,
        rgba(var(--primary-color), 0.13) 0%,
        rgba(var(--primary-color), 0.03) 100%
      ),
      #ffffff;
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
    margin-bottom: 12px;
    padding: 0 20px;
    @apply border border-blue-100;
    img {
      flex-shrink: 0;
      width: 56px;
      height: 56px;
      margin-right: 12px;
    }
    .num {
      font-family: OPPOSans;
      font-size: 24px;
      font-weight: 900;
      line-height: 36px;
      color: #3e4040;
    }
    .title {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: #636466;
    }
  }
</style>
