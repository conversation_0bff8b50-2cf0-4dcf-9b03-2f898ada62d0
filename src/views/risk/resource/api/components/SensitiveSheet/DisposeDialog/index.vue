<template>
  <el-dialog
    title="处置"
    :visible.sync="dialogMixin.visible"
    :custom-class="!recordList.length ? 'el-dialog--mini' : ''"
    append-to-body
    width="800px"
    @closed="onClosed"
  >
    <div v-if="dialogMixin.lazyVisible" class="flex">
      <div class="flex-1 w-0">
        <el-form ref="formRef" :model="formInfo" :rules="formRules" label-width="120px">
          <el-form-item label="处置结果" prop="disposeResult">
            <el-select v-model="formInfo.disposeResult" placeholder="请选择处置结果" clearable>
              <el-option
                v-for="dict in activeDispose.disposeResultDict"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
                :disabled="activeDispose.disposeResultDisabled(dict)"
              />
            </el-select>
            <el-tooltip v-if="activeDispose.disposeResultTips" effect="dark" placement="right">
              <el-link
                :underline="false"
                type="primary"
                icon="el-icon-question"
                class="ml-2"
              />

              <template #content>
                <div class="w-96" v-html="activeDispose.disposeResultTips" />
              </template>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="处置方案" prop="remarks">
            <el-input v-model="formInfo.remarks" type="textarea" placeholder="请输入处置方案" />
          </el-form-item>
        </el-form>
      </div>
      <div v-if="recordList.length" class="flex-1 w-0">
        <div class="">
          <div class="page-detail-title sa-flex sa-row-center sa-m-t-20 sa-m-b-20">流转记录</div>
          <div class="sa-flex sa-row-center">
            <el-timeline class="el-timeline--primary">
              <el-timeline-item
                v-for="(item, index) in recordList"
                :key="index"
                :timestamp="item.createTime"
              >
                {{ item.opUserName }}
                <span v-if="item.sendGroupName">（{{ item.sendGroupName }}）</span>
                {{ item.opDesc }}
                {{ item.receiveGroupName }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'

import request from '@/utils/request.js'

export default {
  dicts: ['pii_status'],
  mixins: [dialogMixin()],
  data() {
    return {
      formInfo: {
        disposeResult: '',
        remarks: ''
      },

      formRules: {
        disposeResult: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      },

      recordList: []

    }
  },
  computed: {
    disposeModel() {
      return {
        'api-sensitive': {
          disposeResultDict: this.dialogMixin.options?.disposeResultDict || this.dict.type.pii_status.filter(
            (item) => ![].includes(item.value)
          ),
          disposeResultDisabled: (dict) => [].includes(String(dict.value))
        }
      }
    },
    activeDispose() {
      return this.disposeModel[this.dialogMixin.params.riskType]
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)

      if (!this.dialogMixin.params.ids) {
        this.getRecordList()
      }
    },
    close() {
      this.dialogMixin.close()
      this.formInfo = this.$options.data().formInfo
    },
    onClosed() {
      this.dialogMixin.reset()
    },
    async submit() {
      await this.$refs.formRef.validate()

      const ids = this.dialogMixin.params.id
        ? [this.dialogMixin.params.id]
        : this.dialogMixin.params.ids

      const params = {
        ...this.formInfo,
        ids
      }

      Object.assign(params, { disposeStatus: params.disposeResult, disposeDesc: params.remarks })
      delete params.disposeResult
      delete params.remarks

      this.dialogMixin.loading = true

      try {
        const res = await request({
          url: '/risk/piis/apiPiisDispose',
          method: 'post',
          data: params
        })

        if (res.code === 200) {
          this.$message.success(res.msg)
          this.dialogMixin.success()
          this.close()
        }
      } catch (error) {
        console.warn(error?.message || error)
      }

      this.dialogMixin.loading = false
    },

    async getRecordList() {
      try {
        const res = await request({
          url: '/risk/turnoverRecord/getRiskDataTurnoverRecord',
          method: 'post',
          data: {
            riskType: this.dialogMixin.params.riskType,
            id: this.dialogMixin.params.id
          }
        })

        this.recordList = res.data
      } catch (error) {
        console.warn(error?.message)
      }
    }
  }
}
</script>

<style></style>
