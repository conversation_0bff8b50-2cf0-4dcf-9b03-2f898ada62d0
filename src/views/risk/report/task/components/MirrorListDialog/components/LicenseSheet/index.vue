<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main" @expand-change="onExpandChange">
    <template #before> </template>

    <template #after>
      <div ref="widthRef" class=""></div>
    </template>

    <template #table:before>
      <el-table-column v-slot="{ row }" type="expand">
        <SbomSheet
          v-if="row.$expand"
          :params="{
            ...params,
            license: row.licenseName
          }"
          layout="table,paging"
          :style="{ width: tableInnerWidth + 'px' }"
        />
      </el-table-column>
    </template>
  </EleSheet>
</template>

<script>
// import {
//   addController,
//   delController,
//   getController,
//   listController,
//   updateController
// } from '@/api/controller/index.js'

import request from '@/utils/request.js'

import SbomSheet from '../SbomSheet/index.vue'

export default {
  components: {
    SbomSheet
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableInnerWidth: 0
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '许可证',

        lazy: false,

        api: {
          list: async(params) => {
            const res = await request({ url: '/risk/basicImage/licenseList', method: 'get', params })

            Object.assign(res, {
              $expand: false
            })

            return res
          },
          export: '/risk/basicImage/exportLicense'
          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // info: getController,
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          export: !this.$checkPermi(['ima:scan:license:export'])
        },

        model: {
          taskId: {
            hidden: true,
            search: {
              value: this.params.taskId
            }
          },
          licenseName: {
            type: 'text',
            label: '许可证名称',
            table: {
              align: 'left'
            },
            search: {},
            form: {
              rules: true
            }
          },
          level: {
            type: 'select',
            label: '风险等级',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: true
            },
            options: [
              { label: '低危', value: '低危', raw: { listClass: 'info' }},
              { label: '中危', value: '中危', raw: { listClass: 'warning' }},
              { label: '高危', value: '高危', raw: { listClass: 'danger' }}
            ]
          },
          dataName: {
            type: 'text',
            label: '镜像名称',
            table: {
              width: 280
            },
            search: {},
            form: {
              rules: true
            }
          },
          // taskName: {
          //   type: 'text',
          //   label: '任务名称',
          //   table: {
          //     width: 200
          //   },
          //   search: {},
          //   form: {
          //     rules: true
          //   }
          // },
          compNum: {
            type: 'number',
            label: '涉及组件数量',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          sourceTool: {
            type: 'text',
            label: '来源工具',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  async mounted() {
    await this.$nextTick()

    this.resizeHandler = () => {
      this.tableInnerWidth = this.$refs.widthRef.getBoundingClientRect().width
    }

    this.resizeHandler()

    window.addEventListener('resize', this.resizeHandler)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler)
    this.resizeHandler = void 0
  },
  methods: {
    async onExpandChange(row, rows) {
      for (let index = 0; index < rows.length; index++) {
        const item = rows[index]
        const expand = item.id === row.id
        this.$set(item, '$expand', expand)
      }
    }
  }
}
</script>

<style></style>
