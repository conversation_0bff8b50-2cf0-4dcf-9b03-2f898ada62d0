<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
// import {
//   addController,
//   delController,
//   getController,
//   listController,
//   updateController
// } from '@/api/controller/index.js'

import request from '@/utils/request.js'

export default {
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    layout: {
      type: String,
      default: 'search,toolbar,table,paging'
    }
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: 'SBOM 清单',

        lazy: false,

        layout: this.layout,

        api: {
          list: async(params) => request({ url: '/risk/basicImage/componentList', method: 'get', params: {
            ...this.$props.params,
            ...params
          }}),
          export: '/risk/basicImage/exportComponent'
          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // info: getController,
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        hiddenActions: {
          export: !this.$checkPermi(['ima:scan:sbom:export'])
        },

        model: {
          taskId: {
            hidden: true,
            search: {
              value: this.params.taskId
            }
          },
          dataName: {
            type: 'text',
            label: '镜像名称',
            table: {
              width: 220
            },
            search: {},
            form: {
              rules: true
            }
          },
          moduleName: {
            type: 'text',
            label: '模块名称',
            align: 'left',
            table: {
              width: 140
            },
            search: {},
            form: {
              rules: true
            }
          },
          compName: {
            type: 'text',
            label: '组件名称',
            align: 'left',
            table: {
              width: 220
            },
            search: {},
            form: {
              rules: true
            }
          },
          compVersion: {
            type: 'text',
            label: '组件版本',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: true
            }
          },
          ecosystem: {
            type: 'text',
            label: '生态',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: true
            }
          },
          //
          // repository: {
          //   type: 'text',
          //   label: '仓库',
          //   table: {
          //     width: 140
          //   },
          //   search: {
          //     hidden: true
          //   },
          //   form: {
          //     rules: true
          //   }
          // },
          openSource: {
            type: 'select',
            label: '是否开源',
            table: {
              width: 100
            },
            search: {},
            form: {
              rules: true
            },
            options: [
              { label: '开源', value: '开源', raw: { listClass: 'success' }},
              { label: '非开源', value: '非开源', raw: { listClass: 'warning' }},
              { label: '未知', value: '未知', raw: { listClass: 'info' }}
            ]
          },
          isThirdParty: {
            type: 'select',
            label: '是否是第三方组件',
            table: {
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            },
            options: [
              { label: '是', value: '是', raw: { listClass: 'success' }},
              { label: '否', value: '否', raw: { listClass: 'info' }}
            ]
          },
          isDirectDependency: {
            type: 'select',
            label: '是否是直接依赖',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: true
            },
            options: [
              { label: '是', value: '是', raw: { listClass: 'success' }},
              { label: '否', value: '否', raw: { listClass: 'info' }}
            ]
          },
          isOnline: {
            type: 'select',
            label: '线上依赖',
            table: {
              width: 100
            },
            search: {},
            form: {
              rules: true
            },
            options: [
              { label: '是', value: '是', raw: { listClass: 'success' }},
              { label: '否', value: '否', raw: { listClass: 'info' }}
            ]
          },
          // compSecScore: {
          //   type: 'number',
          //   label: '组件安全评分',
          //   table: {
          //     width: 120
          //   },
          //   search: {},
          //   form: {
          //     rules: true
          //   }
          // },
          minFixedVersion: {
            type: 'text',
            label: '最小修复版本',
            table: {
              width: 140
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          suggest: {
            type: 'select',
            label: '修复建议',
            table: {
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            },
            options: [
              { label: '强烈建议修复', value: '强烈建议修复', raw: { listClass: 'danger' }},
              { label: '建议修复', value: '建议修复', raw: { listClass: 'warning' }},
              { label: '可选修复', value: '可选修复', raw: { listClass: 'info' }},
              { label: '安全', value: '安全', raw: { listClass: 'success' }}
            ]
          },
          criticalNum: {
            type: 'number',
            label: '严重漏洞数量',
            table: {
              width: 110
            },
            search: {},
            form: {
              rules: false
            }
          },
          highNum: {
            type: 'number',
            label: '高危漏洞数量',
            table: {
              width: 110
            },
            search: {},
            form: {
              rules: false
            }
          },
          mediumNum: {
            type: 'number',
            label: '中危漏洞数量',
            table: {
              width: 110
            },
            search: {},
            form: {
              rules: false
            }
          },
          lowNum: {
            type: 'number',
            label: '低危漏洞数量',
            table: {
              width: 110
            },
            search: {},
            form: {
              rules: false
            }
          },
          repoUrl: {
            type: 'text',
            label: '仓库链接',
            table: {
              width: 220
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          license: {
            type: 'text',
            label: '许可证',
            table: {
              width: 140
            },
            search: {
              value: this.params.license
            },
            form: {
              rules: false
            }
          },
          mavenScope: {
            type: 'text',
            label: '作用域',
            table: {
              width: 100
            },
            search: {},
            form: {
              rules: false
            }
          },
          // taskName: {
          //   type: 'text',
          //   label: '任务名称',
          //   table: {
          //     width: 160
          //   },
          //   search: {
          //     hidden: true
          //   },
          //   form: {
          //     rules: true
          //   }
          // },
          sourceTool: {
            type: 'text',
            label: '来源工具',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
