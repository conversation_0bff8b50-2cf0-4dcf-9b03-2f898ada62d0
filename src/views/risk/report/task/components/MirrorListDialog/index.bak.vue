<template>
  <el-dialog title="查看" :visible.sync="dialogMixin.visible" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <!-- <slot name="before" v-bind="{ search: { ...currentSearch, imageId: dialogMixin.params.imageId } }" /> -->
      <MirrorList
        ref="mirrorListRef"
        v-bind="{
          ...$attrs,
          params: dialogMixin.params,
          source: {
            type: 'safeTask',
          },
        }"
        class="!p-0 !shadow-none"
        @query-success="onQuerySuccess"
        @reset-success="onQuerySuccess"
      />
    </div>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'
import MirrorList from '@/views/risk/basic/mirror/index.vue'
export default {
  mixins: [dialogMixin()],
  components: {
    MirrorList
  },
  data() {
    return {
      currentSearch: {}
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },

    submit() {},

    onQuerySuccess(ctx) {
      this.currentSearch = ctx.parameter()
    }
  }
}
</script>

<style></style>
