<template>
  <el-dialog title="审核" :visible.sync="dialogMixin.visible" custom-class="el-dialog--mini" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <el-form ref="formRef" :model="formInfo" :rules="formRules" label-width="120px">
        <el-form-item label="审核结果" prop="checkStatus">
          <el-radio-group v-model="formInfo.checkStatus">
            <template v-if="['repeat'].includes(dialogMixin.options.type)">
              <el-radio label="3"> 通过 </el-radio>
              <el-radio label="4"> 不通过 </el-radio>
            </template>
            <template v-else>
              <el-radio label="1"> 通过 </el-radio>
              <el-radio label="2"> 不通过 </el-radio>
            </template>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见" prop="checkAdvice">
          <el-input v-model="formInfo.checkAdvice" type="textarea" placeholder="请输入审核方案" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'

import request from '@/utils/request.js'

export default {
  mixins: [dialogMixin()],
  data() {
    return {
      formInfo: {
        checkStatus: '',
        checkAdvice: ''
      },

      formRules: {
        checkStatus: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      }
    }
  },

  computed: {},

  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
      this.formInfo.checkStatus = this.dialogMixin.options.type === 'repeat' ? '3' : '1'
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.formInfo = this.$options.data().formInfo
      this.dialogMixin.reset()
    },
    async submit() {
      await this.$refs.formRef.validate()

      const ids = this.dialogMixin.params.ids

      const params = {
        ...this.dialogMixin.params,
        ...this.formInfo,
        ids
      }

      this.dialogMixin.loading = true

      try {
        const res = await request({ url: '/risk/strategy/check', method: 'post', data: params })

        if (res.code === 200) {
          this.$message.success(res.msg)
          this.dialogMixin.success()
          this.close()
        }
      } catch (error) {
        console.warn(error?.message || error)
      }

      this.dialogMixin.loading = false
    }
  }
}
</script>

<style></style>
