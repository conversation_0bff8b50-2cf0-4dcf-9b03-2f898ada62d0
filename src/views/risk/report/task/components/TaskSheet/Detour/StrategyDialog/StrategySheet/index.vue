<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main" @add-success="onAddSuccess" @edit-success="onEditSuccess">
    <template #before> </template>

    <template #toolbar:after="{ selection, selected }">
      <el-button v-if="$checkPermi(['risk:report:task:detour:strategy:audit']) && !['4'].includes(params.taskStatus)" :disabled="!selected" type="success" icon="el-icon-s-check" @click="onAuditClick(selection)">
        批量初审
      </el-button>
      <el-button v-if="$checkPermi(['risk:report:task:detour:strategy:repeatAudit']) && !['4'].includes(params.taskStatus)" :disabled="!selected" type="success" icon="iconfont icon-piliangshenhe" @click="onAuditClick(selection, 'repeat')">
        批量复审
      </el-button>
    </template>

    <template #table:action:after="{ row }">
      <el-button v-if="['0'].includes(row.checkStatus) && !['4'].includes(params.taskStatus) && $checkPermi(['risk:report:task:detour:strategy:audit'])" type="text" size="mini" @click="onAuditClick([row])">初审</el-button>
      <el-button v-else-if="['1'].includes(row.checkStatus) && !['4'].includes(params.taskStatus) && $checkPermi(['risk:report:task:detour:strategy:repeatAudit'])" type="text" size="mini" @click="onAuditClick([row], 'repeat')">复审</el-button>
      <el-button v-else-if="['2', '4'].includes(row.checkStatus) && !['4'].includes(params.taskStatus) && $checkPermi(['risk:report:task:detour:strategy:resetAudit'])" type="text" size="mini" @click="onResetAuditClick(row)">重新审核</el-button>
    </template>
    <template #after>
      <AuditDialog
        ref="auditDialogRef"
      />
    </template>
  </EleSheet>
</template>

<script>

import {
  addStrategy,
  delStrategy,
  getStrategy,
  listStrategy,
  updateStrategy
} from '@/api/risk/basic/strategy.js'

import AuditDialog from './AuditDialog/index.vue'
import request from '@/utils/request.js'

export default {
  dicts: ['detour_strategy_check_status'],
  components: {
    AuditDialog
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeDeptName: ''
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '绕行报备策略',

        lazy: false,

        api: {
          edit: (params) => updateStrategy({ ...params }),
          list: async(params) => listStrategy({ ...params }),
          info: getStrategy,
          export: '/risk/strategy/export',
          import: '',
          template: ''
          // add: (params) => addStrategy({ ...params }),
          // remove: delStrategy,
        },

        tableProps: {
          selection: true
        },

        hiddenActions: {
          edit: !this.$checkPermi(['risk:report:task:detour:strategy:edit']) || ['4'].includes(this.params.taskStatus),
          export: !this.$checkPermi(['risk:report:task:detour:strategy:export'])
        },

        infoProps: {
          title: true
        },

        flowProps: {
          title: '审批记录',
          preset: 'disposal',
          params: {
            riskType: '24'
          }
        },

        model: {
          taskId: {
            hidden: true,
            value: this.params?.taskId || ''
          },
          deptName: {
            label: '部门名称',
            search: {
              fieldProps: {
                returnName: true
              },
              type: 'CommonDepartmentSelect'
            },
            form: {
              fieldProps: {
                returnName: true,
                on: {
                  change: (value, ctx) => {
                    this.activeDeptName = value
                    ctx.model.systemName = void 0
                  }
                }
              },
              rules: true,
              type: 'CommonDepartmentSelect'
            }
          },
          systemName: {
            label: '业务系统名称',
            search: {
              type: 'CommonSystemSelect',
              fieldProps: {
                returnName: true
              }
            },
            form: {
              rules: true,
              type: 'CommonSystemSelect',
              fieldProps: {
                returnName: true,
                params: {
                  deptName: this.activeDeptName
                }
              }
            }
          },
          accessAccount: {
            type: 'text',
            label: '访问账号',
            table: { width: 140 },
            search: {},
            form: { rules: true }
          },
          selfHostPool: {
            type: 'text',
            label: '本端主机资源池',
            table: { width: 160 },
            search: { hidden: true },
            form: { rules: true }
          },
          selfHostPod: {
            type: 'text',
            label: '本端主机Pod',
            table: { width: 160 },
            search: { hidden: true },
            form: { rules: true }
          },
          selfHostIp: {
            type: 'text',
            label: '本端主机IP',
            table: { width: 140 },
            search: {},
            form: { rules: true }
          },
          selfIpType: {
            label: '本端IP类型',
            table: { width: 120 },
            search: {},
            form: { rules: true }
          },
          selfPort: {
            type: 'number',
            label: '本端端口',
            table: { width: 100 },
            search: {},
            form: { rules: true }
          },
          oppositeHostPool: {
            type: 'text',
            label: '对端主机资源池',
            table: { width: 160 },
            search: { hidden: true },
            form: { rules: true }
          },
          oppositeHostPod: {
            type: 'text',
            label: '对端主机Pod',
            table: { width: 160 },
            search: { hidden: true },
            form: { rules: true }
          },
          oppositeHostIp: {
            type: 'text',
            label: '对端主机IP',
            table: { width: 140 },
            search: {},
            form: { rules: true }
          },
          oppositeIpType: {
            label: '对端IP类型',
            table: { width: 120 },
            search: {},
            form: { rules: true }
          },
          oppositePort: {
            type: 'number',
            label: '对端端口',
            table: { width: 100 },
            search: {},
            form: { rules: true }
          },
          accessWay: {
            label: '访问方式',
            table: { width: 120 },
            search: {},
            form: { rules: true }
          },
          recordAccessRate: {
            type: 'text',
            label: '备案访问频率',
            table: { width: 120 },
            search: {},
            form: { rules: true }
          },
          recordTimePeriod: {
            type: 'text',
            label: '备案时间段',
            table: { width: 140 },
            search: {
              hidden: true
            },
            form: {
              rules: true
              // type: 'time-range',
              // fieldProps: {
              //   valueFormat: 'H：mm',
              //   format: 'H：mm'
              // },
              // formatter: (data) => {
              //   return data.recordTimePeriod ? data.recordTimePeriod.split('~') : []
              // },
              // parameter: (value) => {
              //   if (value?.length) {
              //     return `${value[0]}~${value[1]}`
              //   }
              //   return ''
              // }
            }
          },
          recordPeople: {
            type: 'text',
            label: '备案人',
            table: { width: 100 },
            search: {},
            form: { rules: true }
          },
          approver: {
            type: 'text',
            label: '审批人',
            table: { width: 100 },
            search: {},
            form: { rules: true }
          },
          phone: {
            type: 'text',
            label: '联系方式',
            table: { width: 120 },
            search: {},
            form: { rules: true }
          },
          effectTime: {
            label: '生效时间',
            table: { width: 160 },
            search: {},
            hidden: ['form', 'search']
          },
          lapseTime: {
            label: '失效时间',
            table: { width: 160 },
            search: {},
            hidden: ['form', 'search']
          },
          effectiveTimeRange: {
            label: '生效时间范围',
            type: 'date-time-range',
            parameter: (data) => {
              return {
                effectTime: data?.[0],
                lapseTime: data?.[1]
              }
            },
            formatter: (value, ctx) => {
              return value.lapseTime ? [value.effectTime, value.lapseTime] : []
            },
            search: {
            },
            form: {
              rules: true
            },
            hidden: ['table', 'info']
          },
          recordSceneExplain: {
            type: 'text',
            label: '备案场景说明',
            table: { width: 200 },
            search: { hidden: true },
            form: { rules: true }
          },
          checkStatus: {
            type: 'select',
            label: '审核状态',
            table: { width: 120, tableColumnProps: { fixed: 'right' }},
            search: {},
            form: { hidden: true },
            options: this.dict.type.detour_strategy_check_status,
            group: 'flow'
          },
          checkAdvice: {
            type: 'text',
            label: '审核意见',
            table: { tableColumnProps: { fixed: 'right' }},
            search: { hidden: true },
            form: { hidden: true },
            group: 'flow'
          },
          createUser: {
            type: 'text',
            label: '创建人',
            table: { width: 100 },
            search: {},
            form: { hidden: true }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }
        }
      }

      return value
    }
  },
  methods: {
    onAddSuccess() {
      this.activeDeptName = ''
    },
    onEditSuccess() {
      this.activeDeptName = ''
    },
    onAuditClick(rows, type) {
      const ids = rows.map(item => item.id)

      this.$refs.auditDialogRef.open({
        type,
        params: {
          ...this.params,
          ids
        },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    },
    async onResetAuditClick(row) {
      try {
        await this.$confirm('是否重新审核？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const res = await request({
          url: '/risk/strategy/check',
          method: 'post',
          data: {
            ...this.params,
            ids: [row.id],
            checkStatus: '0'
          }
        })

        if (res.code === 200) {
          this.$message.success(res.msg)
          this.$refs.sheetRef.getTableData()
        } else {
          this.$message.error(res.msg || '操作失败')
        }
      } catch (error) {
        return false
      }
    }
  }

}
</script>

<style></style>
