<template>
  <el-dialog title="详情" :visible.sync="dialogMixin.visible" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <StrategySheet
        ref="strategySheetRef"
        v-bind="{
          params: dialogMixin.params,
        }"
        class="page-main--flat"
      />
    </div>
    <!-- <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template> -->
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'
import StrategySheet from './StrategySheet/index.vue'
export default {
  mixins: [dialogMixin()],
  components: {
    StrategySheet
  },
  data() {
    return {}
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },
    submit() {}
  }
}
</script>

<style></style>
