<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--full" @add-success="onAddSuccess" @edit-success="onEditSuccess">
    <template #before="{ searchMixin: { lazyModel, parameter } }">
      <TaskStats :key="[taskType, JSON.stringify(lazyModel)].join(',')" v-bind="{ queryParams: { ...parameter(), taskType } }" />
    </template>

    <template #toolbar:after="{ singleSelected, selectionIds, selection, removeHandler }">
      <el-button
        v-hasPermi="['risk:report:task:del']"
        type="danger"
        icon="el-icon-delete"
        :disabled="!singleSelected"
        @click="removeHandler(selectionIds)"
      >删除
      </el-button>

      <el-button
        v-hasPermi="['risk:report:task:invalidate']"
        type="warning"
        icon="el-icon-document-delete"
        :disabled="!singleSelected"
        @click="onVoidClick(selectionIds, selection)"
      >作废
      </el-button>
    </template>

    <template #table:taskName:simple="{ row }">
      <span class="text-primary-500 hover:underline cursor-pointer" @click="handleEdit(row)">
        {{ row.taskName }}
      </span>
    </template>

    <template #info:deployFiles:simple="{ data }">
      <span class="text-primary-500 hover:underline cursor-pointer" @click="handleDownload(data)">
        {{ data.uploadFileName }}
      </span>
    </template>

    <template #table:after="{ infoHandler }">
      <el-table-column v-slot="{ row }" label="操作" fixed="right" align="center" width="150">
        <el-button type="text" size="mini" @click="onViewClick(row)">详情</el-button>

        <el-button v-if="$checkPermi(['risk:report:confirmation:statement']) && ['11'].includes(row.taskStatus)" type="text" size="mini" @click="handleStatement(row)">确认结单</el-button>
      </el-table-column>
    </template>

    <!-- <template #add:after="{ model }">
      <TabGroup
        :key="model.id"
        v-bind="{
          fileTabName: '上传APP安装包',
          formInfo: model,
          excludeColumns: ['status', 'updateTime']
        }"
      />
    </template> -->

    <template #edit:after="{ model }">
      <TabGroup
        v-if="model.taskStatus && !['4'].includes(model.taskStatus)"
        :key="model.id"
        v-bind="{
          fileTabName: '绕行报备附件',
          fileColumnName:'文件名',
          formInfo: model,
          excludeColumns: ['status', 'updateTime'],
          hiddenApproval: true
        }"
      />
      <!-- 'reportFileType', -->
    </template>

    <template #after>
      <ReportImportDialog ref="reportImportDialogRef" @success="handleRefresh" />
      <StrategyDialog ref="strategyDialogRef" @success="handleRefresh" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { dataMixin } from '@/mixins'

import { listTask, getTask, addTask, updateTask, delTask } from '@/api/risk/report/task.js'

import TaskStats from '@/views/risk/report/task/components/TaskStats/index.vue'
import TabGroup from '@/views/risk/report/task/components/TabGroup/index.vue'
import ReportImportDialog from '@/views/risk/report/url/ImportDialog/index.vue'

import StrategyDialog from './StrategyDialog/index.vue'

export default {
  dicts: ['main_task_type', 'task_status_19', 'work_order_type'],
  components: {
    TaskStats,
    TabGroup,
    ReportImportDialog,
    StrategyDialog
  },
  mixins: [
    dataMixin({
      projectManagerList: {
        default: [],
        async load() {
          const res = await request({
            url: '/risk/report/task/getProjectManagerList',
            method: 'get',
            params: {
              type: 'strategy'
            }
          })

          const data = (res.data || []).map(item => {
            const label = item.primaryAccountNumber ? `${item.primaryAccountNumber}-${item.nickName}` : item.nickName

            return {
              label,
              value: item.userId
            }
          })

          return data
        }
      }
    })
  ],

  props: {
    taskType: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      activeDeployType: '1',
      activeDeptName: ''
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '绕行报备任务',

        lazy: false,

        api: {
          add: async(params) => {
            return addTask(params)
          },
          edit: (params) => updateTask({ ...params }),
          list: async(params) => listTask({ ...params, taskType: this.taskType }),
          info: getTask,
          remove: delTask,
          export: (handler) => handler('/risk/report/task/export', { parameter: (params) => ({ ...params, taskType: this.taskType }) }),
          import: '',
          template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          height: '100%'
        },

        editProps: {
          disabled: !this.$checkPermi(['risk:report:task:edit'])
        },

        hiddenActions: {
          add: !this.$checkPermi(['risk:report:task:add']),
          edit: true,
          remove: !this.$checkPermi(['risk:report:task:edl']),
          export: !this.$checkPermi(['risk:report:task:export'])
        },

        model: {
          taskType: {
            label: '任务类型',
            type: 'select',
            width: 150,
            options: this.dict.type.main_task_type.filter((item) => this.taskType == item.value),
            value: this.taskType,
            form: {
              rules: true,
              fieldProps: {
                disabled: true
              }
            },
            search: {
              hidden: true
            },
            table: {
              hidden: true
            }
          },
          taskName: {
            label: '任务名称',
            type: 'text',
            form: {
              rules: true,
              hidden: false
            },
            table: {
              align: 'left'
            },
            edit: {
              fieldProps: {
                disabled: true
              }
            }
          },
          taskStatus: {
            type: 'select',
            label: '任务状态',
            form: {
              hidden: true
            },
            options: this.dict.type.task_status_19
          },
          deptName: {
            label: '所属部门',
            search: {
              fieldProps: {
                returnName: true
              },
              type: 'CommonDepartmentSelect'
            },
            form: {
              fieldProps: {
                returnName: true,
                on: {
                  change: (value, ctx) => {
                    this.activeDeptName = value
                    ctx.model.systemName = void 0
                  }
                }
              },
              rules: true,
              type: 'CommonDepartmentSelect'
            }
          },
          systemName: {
            label: '业务系统',
            search: {
              type: 'CommonSystemSelect',
              fieldProps: {
                returnName: true
              }
            },
            form: {
              rules: true,
              type: 'CommonSystemSelect',
              fieldProps: {
                returnName: true,
                params: {
                  deptName: this.activeDeptName
                }
              }
            }
          },
          projectManagerId: {
            label: '局方项目经理',
            type: 'select',
            table: {
              type: 'text',
              formatter: (row) => {
                return row.projectManager || '-'
              }
            },
            form: {
              rules: true
            },
            fieldProps: {
              allowCreate: true,
              filterable: true
            },
            info: {
              type: 'text',
              formatter: (row) => {
                return row.projectManager || '-'
              }
            },
            options: this.dataMixin.projectManagerList
          },
          createBy: {
            label: '创建人',
            type: 'text',
            form: {
              hidden: true,
              rules: true
            }
          },
          remarks: {
            label: '备注',
            search: {
              hidden: true
            },
            table: {
            },
            form: {
              type: 'textarea'
            }
          },
          createTime: {
            type: 'text',
            label: '创建时间',
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            },
            form: {
              hidden: true
            },
            width: 200
          }

        }
      }

      return value
    }
  },
  methods: {
    checkRolePersonType(values = [], { reversed = false } = {}) {
      const { personType, role } = this.$store.getters.userInfo

      if (['1', '2'].includes(String(role.roleType))) {
        return true
      }

      if (reversed) {
        if (!values.includes(String(personType))) {
          return true
        }
      } else {
        if (values.includes(String(personType))) {
          return true
        }
      }

      return false
    },
    onAddSuccess() {
      this.activeDeptName = ''
    },
    onEditSuccess() {
      this.activeDeptName = ''
    },
    async generateTaskId() {
      const res = await request({
        url: '/risk/report/task/getTaskNextId'
      })

      return res?.nextId
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleAudit(row) {
      this.$refs.auditDialogRef.open({
        params: {
          ...row,
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    handleAuditRepeat(row) {
      this.$refs.auditRepeatDialogRef.open({
        params: {
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    handlePublish(row) {
      this.$refs.publishDialogRef.open({
        params: {
          ...row,
          taskId: row.id,
          taskType: row.taskType
        },
        success: () => {
          this.handleRefresh()
        }
      })
    },
    async handleStatement(row) {
      try {
        await this.$confirm('是否确认已结单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/report/task/confirmationStatement`,
        method: 'post',
        data: {
          id: row.id,
          taskType: row.taskType,
          taskStatus: 4
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      } else {
        this.$message.warning(res.msg)
      }
    },
    handleDownload(row) {
      if (!row.uploadFileUrl) return false

      window.open(row.uploadFileUrl)
    },

    async onVoidClick(ids, selection) {
      const disabledRow = selection.find(item => ['4'].includes(String(item.taskStatus)))

      if (disabledRow) {
        this.$message.warning(`任务名称：${disabledRow.taskName} 的任务类型不支持作废`)
        return false
      }

      try {
        await this.$confirm('是否确认作废？', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return error
      }

      const res = await request({
        url: `/risk/report/task/invalidate`,
        method: 'post',
        params: {
          taskId: ids[0]
        }
      })

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.handleRefresh()
      }
    },
    onReportClick(row) {
      this.$refs.reportImportDialogRef.open({
        params: row
      })
    },
    onViewClick(row) {
      this.$refs.strategyDialogRef.open({
        params: {
          taskId: row.id,
          taskType: row.taskType,
          taskStatus: row.taskStatus
        }
      })
    }
  }
}
</script>

<style></style>
