<template>
  <el-dialog title="添加字段" :visible.sync="dialogMixin.visible" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <el-form ref="formRef" :model="formInfo" :rules="formRules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="属性字段类型" prop="fieldType">
              <el-select v-model="formInfo.fieldType" placeholder="请选择属性字段类型">
                <el-option label="大文本" value="text"></el-option>
                <el-option label="字符串" value="varchar"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="属性字段数量" prop="fieldNum">
              <el-input-number v-model="formInfo.fieldNum" placeholder="请输入属性字段数量" :min="1" :max="100"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'

import request from '@/utils/request.js'

export default {
  mixins: [dialogMixin()],
  data() {
    return {
      formInfo: {
        fieldType: 'text',
        fieldNum: 0
      },
      formRules: {
        fieldType: [
          { required: true, message: '请选择属性字段类型', trigger: 'change' }
        ],
        fieldNum: [
          { required: true, message: '请输入属性字段数量', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },
    async submit() {
      try {
        await this.$refs.formRef.validate()
      } catch (error) {
        return false
      }

      this.dialogMixin.loading = true

      try {
        const res = await request({
          url: '/risk/securityAudit/addDbFieldInfo',
          method: 'get',
          params: {
            ...this.formInfo
          }
        })

        if (res.code === 200) {
          this.$message.success(res.msg)
          this.dialogMixin.loading = false
          this.dialogMixin.close()
          this.dialogMixin.success(res.data)
        } else {
          this.$message.error(res.msg || '操作失败，请稍后再试')
        }
      } finally {
        this.dialogMixin.loading = false
      }
    }
  }
}
</script>

<style></style>
