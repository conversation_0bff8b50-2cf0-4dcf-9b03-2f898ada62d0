<template>
  <el-dialog title="分配省公司" :visible.sync="dialogMixin.visible" append-to-body width="800px" @closed="onClosed">
    <div v-if="dialogMixin.lazyVisible" class="">
      <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main page-main--flat">
        <template #toolbar:before="{selected, selection, addHandler}">
          <el-button type="primary" icon="el-icon-plus" @click="addHandler">添加省公司</el-button>
          <el-button type="warning" :disabled="!selected" icon="el-icon-circle-close" @click="onCompanyUnAssign(selection)">取消分配</el-button>
        </template>

        <template #table:after>
          <el-table-column v-slot="{ row }" label="操作" width="100" align="center" fixed="right">
            <el-button type="text" size="mini" @click="onCompanyUnAssign([row])">取消分配</el-button>
          </el-table-column>
        </template>

        <template #form:provincialCompanyName:simple="{ model }">
          <CommonDepartmentSelect
            v-model="model.provincialCompanyName"
            return-name
            placeholder="请选择省公司"
            @current-change="(info) => onDepartmentChange(info, model)"
          />
        </template>
      </EleSheet>
    </div>

  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'
import request from '@/utils/request.js'

export default {
  mixins: [dialogMixin()],
  data() {
    return {}
  },
  computed: {
    params() {
      return {
        ...this.dialogMixin.params
      }
    },
    sheetProps() {
      return {
        title: '分配省公司',
        lazy: false,
        api: {
          list: (params) => request({
            url: '/system/company/list',
            method: 'get',
            params: {
              ...params
            }
          }),
          add: (params) => request({
            url: '/system/company',
            method: 'post',
            data: params
          })
        },
        tableProps: {
          selection: true
        },
        idKey: 'id',
        hiddenActions: {
          add: true
        },
        model: {
          roleId: {
            hidden: true,
            value: this.params.roleId
          },
          roleName: {
            label: '角色名称',
            table: {
              align: 'left'
            },
            hidden: ['search', 'form'],
            value: this.params.roleName
          },
          provincialCompanyName: {
            label: '省公司名称',
            table: {
              align: 'left'
            }
          },
          provincialCompanyId: {
            hidden: true
          },
          createBy: {
            label: '创建人',
            hidden: ['search', 'form']
          },
          createTime: {
            label: '创建时间',
            hidden: ['search', 'form']
          }
        }
      }
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },

    onDepartmentChange(info, model) {
      this.$set(model, 'provincialCompanyId', info.deptId)
    },

    async onCompanyUnAssign(rows) {
      const ids = rows.map(item => item.id).join(',')

      try {
        await this.$confirm('是否取消分配选中的省公司？', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return false
      }

      const res = await request({
        url: `/system/company/${ids}`,
        method: 'delete'
      })

      if (res.code == 200) {
        this.$message.success(res.msg)
        this.$refs.sheetRef.getTableData()
      }
    }
  }
}
</script>

<style></style>
