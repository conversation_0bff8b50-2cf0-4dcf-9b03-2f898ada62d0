<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:before="{ selection, selected }">
      <el-button
        v-hasPermi="['img:loophole:all:dispose']"
        type="primary"
        :disabled="!selected"
        @click="handleDispose(selection)"
      ><IconHandyman class="" />处置
      </el-button>
    </template>

    <template #table:action:after="{ row }">
      <el-button
        v-hasPermi="['img:loophole:all:dispose']"
        size="mini"
        type="text"
        @click="handleDispose([row])"
      >处置
      </el-button>
    </template>

    <template #after>
      <DisposeDialog ref="disposeDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import {
  addLoophole,
  delLoophole,
  exportLoophole,
  getLoophole,
  listLoophole,
  updateLoophole
} from '@/api/ima/loophole'

import request from '@/utils/request.js'

import DisposeDialog from '@/views/ima/loophole/components/DisposeDialog/index.vue'

import { disposeType as disposeTypeDict } from '@/dicts/common/index.js'

export default {
  dicts: ['loophole_type'],
  components: {
    DisposeDialog
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    listApi: {
      type: [String, Function],
      default: void 0
    }
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '亚信智网扫描漏洞',

        lazy: false,

        api: {
          list: async(params) => request({
            url: this.listApi || '/ima/loophole/list',
            method: 'get',
            params
          }),
          info: getLoophole
          // add: (params) => addController({ ...params }),
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // export: '/controller/export',
          // import: '',
          // template: '',
        },

        infoProps: {
          title: true
        },

        tableProps: {
          selection: true
        },

        model: {
          taskDetailId: {
            hidden: true,
            value: this.params.taskDetailId
          },
          scanRecordId: {
            hidden: true,
            value: this.params.scanRecordId
          },
          scanType: {
            hidden: true,
            value: '3'
          },
          imgId: {
            type: 'text',
            label: '镜像ID',
            table: {
              width: 180
            },
            search: {
              hidden: true
            },
            form: {
              rules: true
            }
          },
          imageName: {
            type: 'text',
            label: '镜像名称',
            table: {
              width: 160
            },
            search: {},
            form: {
              rules: true
            }
          },
          cveCode: {
            type: 'text',
            label: '漏洞名称',
            table: {
              width: 200
            },
            search: {},
            form: {
              rules: true
            }
          },
          severity: {
            type: 'text',
            label: 'CVE严重性',
            table: {
              width: 100
            },
            search: {},
            form: {
              rules: true
            }
            // options: [
            //   { label: '低', value: 'Low' },
            //   { label: '中', value: 'Medium' },
            //   { label: '高', value: 'High' },
            //   { label: '严重', value: 'Critical' }
            // ]
          },
          cveId: {
            type: 'text',
            label: 'CVE编号',
            table: {
              width: 160
            },
            search: {},
            form: {
              rules: true
            }
          },
          packageName: {
            type: 'text',
            label: '安全包名称',
            table: {
              width: 180
            },
            search: {},
            form: {
              rules: true
            }
          },
          type: {
            type: 'select',
            label: '漏洞类型',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: true
            },
            options: this.dict.type.loophole_type
          },
          version: {
            type: 'text',
            label: '安装版本',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: false
            }
          },
          versionFixed: {
            type: 'text',
            label: '修复版本',
            table: {
              width: 120
            },
            search: {},
            form: {
              rules: false
            }
          },
          cnnvdName: {
            type: 'text',
            label: '软件严重性',
            table: {
              width: 140
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          fixSuggestion: {
            type: 'text',
            label: '攻击复杂度',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          attackVector: {
            type: 'text',
            label: '攻击向量',
            table: {
              width: 120
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          references: {
            type: 'text',
            label: '组件路径',
            table: {
              width: 200
            },
            search: {
              hidden: true
            },
            form: {
              rules: false
            }
          },
          disposeStatus: {
            label: '处置状态',
            type: 'select',
            options: disposeTypeDict,
            tableColumnProps: {
              fixed: 'right'
            }
          }
          // createTime: {
          //   type: 'text',
          //   label: '创建时间',
          //   search: {
          //     type: 'date-range',
          //     parameter: (data) => {
          //       return {
          //         startTime: data?.[0],
          //         endTime: data?.[1]
          //       }
          //     }
          //   },
          //   form: {
          //     hidden: true
          //   },
          //   width: 200
          // }
        }
      }

      return value
    }
  },
  methods: {
    handleDispose(rows) {
      const ids = rows.map(item => item.cveCode)
      this.$refs.disposeDialogRef.open({
        params: { ids },
        success: () => {
          this.$refs.sheetRef.getTableData()
        }
      })
    }
  }
}
</script>

<style></style>
